<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find Jobs - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>

</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-primary font-medium">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-primary transition flex items-center">
                            For Employers <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="login-dashboard.html?redirect=dashboard/employer.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-t-lg">Dashboard</a>
                            <a href="post-job.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-b-lg">Post a Job</a>
                        </div>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-primary transition">Sign In</a>
                    <a href="signup.html" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">Sign Up</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-gray-700">Home</a>
                <a href="jobs.html" class="block py-2 text-primary font-medium">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
                <a href="dashboard/employer.html" class="block py-2 text-gray-700">For Employers</a>
                <div class="border-t pt-2 mt-2">
                    <a href="login.html" class="block py-2 text-gray-700">Sign In</a>
                    <a href="signup.html" class="block py-2 text-primary font-medium">Sign Up</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Search Header -->
    <section class="bg-white py-8 border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row xl:flex-row 2xl:flex-row gap-6">
                <!-- Search Bar -->
                <div class="flex-1 bg-gray-50 rounded-lg p-4 xl:p-6 2xl:p-8">
                    <div class="flex flex-col md:flex-row xl:flex-row 2xl:flex-row gap-4 xl:gap-6 2xl:gap-8">
                        <div class="flex-1">
                            <div class="relative">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" placeholder="Job title, keywords, or company"
                                       class="search-input w-full pl-10 pr-4 py-3 xl:py-4 2xl:py-5 text-gray-900 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent xl:text-lg 2xl:text-xl">
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="relative">
                                <i class="fas fa-map-marker-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" placeholder="City, state, or zip code"
                                       class="search-input w-full pl-10 pr-4 py-3 xl:py-4 2xl:py-5 text-gray-900 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent xl:text-lg 2xl:text-xl">
                            </div>
                        </div>
                        <button class="search-btn bg-primary text-white px-8 py-3 xl:px-10 xl:py-4 2xl:px-12 2xl:py-5 rounded-lg hover:bg-secondary transition font-medium xl:text-lg 2xl:text-xl">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>
                    </div>
                </div>

                <!-- Sort Options -->
                <div class="lg:w-64 xl:w-80 2xl:w-96">
                    <select id="sort-select" class="w-full px-4 py-3 xl:px-6 xl:py-4 2xl:px-8 2xl:py-5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent xl:text-lg 2xl:text-xl">
                        <option value="relevance">Sort by: Relevance</option>
                        <option value="date">Sort by: Date Posted</option>
                        <option value="salary-high">Sort by: Salary (High to Low)</option>
                        <option value="salary-low">Sort by: Salary (Low to High)</option>
                        <option value="company">Sort by: Company Name</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Filters Sidebar -->
            <div class="lg:w-80">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-24">
                    <h3 class="font-semibold text-gray-900 mb-4">Filters</h3>
                    
                    <!-- Job Type -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Job Type</h4>
                        <div class="space-y-2" id="job-type-filters">
                            <!-- Dynamic job type filters will be inserted here -->
                        </div>
                    </div>

                    <!-- Salary Range -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Salary Range</h4>
                        <div class="space-y-2" id="salary-filters">
                            <!-- Dynamic salary filters will be inserted here -->
                        </div>
                    </div>

                    <!-- Experience Level -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Experience Level</h4>
                        <div class="space-y-2" id="experience-filters">
                            <!-- Dynamic experience filters will be inserted here -->
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Location</h4>
                        <div class="space-y-2" id="location-filters">
                            <!-- Dynamic location filters will be inserted here -->
                        </div>
                    </div>

                    <!-- Clear Filters -->
                    <button class="clear-filters-btn w-full bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition">
                        Clear All Filters
                    </button>
                </div>
            </div>

            <!-- Job Listings -->
            <div class="flex-1">
                <!-- Results Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Job Results</h2>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="grid-view-btn" class="p-2 text-gray-400 hover:text-primary transition" title="Grid View">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button id="list-view-btn" class="p-2 text-primary" title="List View">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- Job Cards Container (Dynamic) -->
                <div class="space-y-4" id="jobs-container">
                    <!-- Jobs will be rendered here dynamically -->
                </div>



                <!-- Pagination -->
                <div class="flex justify-center mt-8" id="pagination-container">
                    <nav class="flex items-center space-x-2" id="pagination-nav">
                        <!-- Pagination will be generated dynamically -->
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities across the United States.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <li><a href="login-dashboard.html?redirect=dashboard/candidate.html" class="hover:text-white transition">My Dashboard</a></li>
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-materials.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="login-dashboard.html?redirect=dashboard/employer.html" class="hover:text-white transition">Employer Dashboard</a></li>
                        <li><a href="post-job.html" class="hover:text-white transition">Post a Job</a></li>
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Help Center</a></li>
                        <!-- <li><a href="#" class="hover:text-white transition">Contact Us</a></li> -->
                        <li><a href="#" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        // EasyNaukri namespace for utility functions
        window.EasyNaukri = {
            // Show notification
            showNotification: function(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

                const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
                const icon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';

                notification.className += ` ${bgColor} text-white`;
                notification.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas ${icon} mr-3"></i>
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }, 5000);
            }
        };

        // Dynamic job data - 10 sample jobs
        let allJobs = [
            {
                id: 1,
                title: "Senior Software Engineer",
                company: "Google Inc.",
                location: "Mountain View, CA",
                salary: 150000,
                salaryRange: "100k+",
                posted: "2024-01-20",
                applicants: 50,
                description: "We're looking for a Senior Software Engineer to join our team and help build the next generation of our products. You'll work on challenging problems at scale...",
                skills: ["JavaScript", "React", "Node.js", "Python", "AWS"],
                type: "Full-time",
                experience: "Senior"
            },
            {
                id: 2,
                title: "Product Manager",
                company: "Amazon",
                location: "Seattle, WA",
                salary: 85000,
                salaryRange: "75k-100k",
                posted: "2024-01-18",
                applicants: 35,
                description: "Join our product team to drive the strategy and execution of our flagship products. You'll work closely with engineering, design, and business teams...",
                skills: ["Product Strategy", "Analytics", "SQL", "Agile", "Leadership"],
                type: "Full-time",
                experience: "Mid"
            },
            {
                id: 3,
                title: "Data Scientist",
                company: "Microsoft",
                location: "Redmond, WA",
                salary: 120000,
                salaryRange: "100k+",
                posted: "2024-01-15",
                applicants: 28,
                description: "We're seeking a talented Data Scientist to join our machine learning team. You'll work on cutting-edge AI projects that impact millions of customers...",
                skills: ["Python", "Machine Learning", "SQL", "TensorFlow", "Statistics"],
                type: "Full-time",
                experience: "Senior"
            },
            {
                id: 4,
                title: "Frontend Developer",
                company: "Meta",
                location: "Menlo Park, CA",
                salary: 65000,
                salaryRange: "50k-75k",
                posted: "2024-01-22",
                applicants: 42,
                description: "Build beautiful and responsive user interfaces for our social media platforms. Work with React, TypeScript, and modern web technologies...",
                skills: ["React", "TypeScript", "CSS", "JavaScript", "GraphQL"],
                type: "Full-time",
                experience: "Mid"
            },
            {
                id: 5,
                title: "DevOps Engineer",
                company: "Netflix",
                location: "Los Gatos, CA",
                salary: 140000,
                salaryRange: "100k+",
                posted: "2024-01-19",
                applicants: 31,
                description: "Help us scale our infrastructure to serve millions of users worldwide. Work with cloud technologies, automation, and monitoring systems...",
                skills: ["AWS", "Docker", "Kubernetes", "Python", "Terraform"],
                type: "Remote",
                experience: "Senior"
            },
            {
                id: 6,
                title: "UX Designer",
                company: "Apple",
                location: "Cupertino, CA",
                salary: 115000,
                salaryRange: "100k+",
                posted: "2024-01-17",
                applicants: 25,
                description: "Design intuitive and beautiful user experiences for our consumer products. Collaborate with product managers and engineers to bring ideas to life...",
                skills: ["Figma", "Sketch", "Prototyping", "User Research", "Design Systems"],
                type: "Full-time",
                experience: "Senior"
            },
            {
                id: 7,
                title: "Backend Developer",
                company: "Spotify",
                location: "New York, NY",
                salary: 45000,
                salaryRange: "30k-50k",
                posted: "2024-01-21",
                applicants: 38,
                description: "Build scalable backend services for our music streaming platform. Work with microservices, databases, and high-performance systems...",
                skills: ["Java", "Spring Boot", "PostgreSQL", "Redis", "Kafka"],
                type: "Full-time",
                experience: "Entry"
            },
            {
                id: 8,
                title: "Mobile Developer",
                company: "Uber",
                location: "San Francisco, CA",
                salary: 35000,
                salaryRange: "30k-50k",
                posted: "2024-01-16",
                applicants: 33,
                description: "Develop mobile applications for iOS and Android platforms. Work on features that millions of users interact with daily...",
                skills: ["Swift", "Kotlin", "React Native", "iOS", "Android"],
                type: "Part-time",
                experience: "Entry"
            },
            {
                id: 9,
                title: "Machine Learning Engineer",
                company: "Tesla",
                location: "Austin, TX",
                salary: 145000,
                salaryRange: "100k+",
                posted: "2024-01-14",
                applicants: 29,
                description: "Work on autonomous driving technology and AI systems. Implement machine learning models for real-world applications...",
                skills: ["Python", "TensorFlow", "PyTorch", "Computer Vision", "Deep Learning"],
                type: "Full-time",
                experience: "Senior"
            },
            {
                id: 10,
                title: "Junior Developer",
                company: "IBM",
                location: "Austin, TX",
                salary: 40000,
                salaryRange: "30k-50k",
                posted: "2024-01-13",
                applicants: 22,
                description: "Start your career in tech! Learn and grow while working on enterprise software solutions with experienced mentors...",
                skills: ["JavaScript", "HTML", "CSS", "Git", "React"],
                type: "Full-time",
                experience: "Entry"
            }

        ];

        let filteredJobs = [...allJobs];
        let currentPage = 1;
        let jobsPerPage = 10;
        let currentView = 'list';

        // Function to load posted jobs from localStorage
        function loadPostedJobs() {
            try {
                const postedJobs = JSON.parse(localStorage.getItem('postedJobs')) || [];
                console.log('Loaded posted jobs from localStorage:', postedJobs.length);

                // Add posted jobs to the beginning of allJobs array (most recent first)
                if (postedJobs.length > 0) {
                    // Remove any existing posted jobs to avoid duplicates
                    allJobs = allJobs.filter(job => job.postedBy !== 'employer');

                    // Add posted jobs to the beginning
                    allJobs = [...postedJobs, ...allJobs];

                    console.log('Total jobs after merging:', allJobs.length);
                }
            } catch (error) {
                console.error('Error loading posted jobs from localStorage:', error);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing jobs...');

            // Load posted jobs from localStorage and merge with static jobs
            loadPostedJobs();

            console.log('Total jobs available:', allJobs.length);

            // Initialize filteredJobs with all jobs
            filteredJobs = [...allJobs];

            // Generate dynamic filters first
            generateDynamicFilters();

            // Initialize dynamic job system
            initializeDynamicJobs();

            // Initialize sort functionality
            initializeSortBy();

            // Initialize view toggle functionality
            initializeViewToggle();

            // Initial render - this will show the jobs
            renderJobs();
            updateJobCount();
            updatePagination();

            // Initialize main functionality after jobs are rendered
            if (typeof EasyNaukri !== 'undefined') {
                EasyNaukri.initializeSearch();
                EasyNaukri.initializeBookmarks();
                EasyNaukri.initializeCheckboxFilters();
            }

            console.log('Jobs page initialized successfully');
        });

        // Generate dynamic filters based on job data
        function generateDynamicFilters() {
            // Generate job type filters
            const jobTypes = {};
            allJobs.forEach(job => {
                jobTypes[job.type] = (jobTypes[job.type] || 0) + 1;
            });

            const jobTypeContainer = document.getElementById('job-type-filters');
            if (jobTypeContainer) {
                jobTypeContainer.innerHTML = '';
                Object.entries(jobTypes).forEach(([type, count]) => {
                    const label = document.createElement('label');
                    label.className = 'flex items-center';
                    label.innerHTML = `
                        <input type="checkbox" name="job-type" value="${type}" class="rounded border-gray-300 text-primary focus:ring-primary">
                        <span class="ml-2 text-gray-700">${type} (${count})</span>
                    `;
                    jobTypeContainer.appendChild(label);
                });
            }

            // Generate salary range filters
            const salaryRanges = {};
            allJobs.forEach(job => {
                salaryRanges[job.salaryRange] = (salaryRanges[job.salaryRange] || 0) + 1;
            });

            const salaryContainer = document.getElementById('salary-filters');
            if (salaryContainer) {
                salaryContainer.innerHTML = '';
                const salaryOrder = ['30k-50k', '50k-75k', '75k-100k', '100k+'];
                salaryOrder.forEach(range => {
                    if (salaryRanges[range]) {
                        const label = document.createElement('label');
                        label.className = 'flex items-center';
                        const displayText = range === '100k+' ? '$100,000+' :
                                          range.replace('k', ',000').replace('-', ' - $').replace(/^/, '$');
                        label.innerHTML = `
                            <input type="checkbox" name="salary" value="${range}" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-gray-700">${displayText} (${salaryRanges[range]})</span>
                        `;
                        salaryContainer.appendChild(label);
                    }
                });
            }

            // Generate experience level filters
            const experienceLevels = {};
            allJobs.forEach(job => {
                experienceLevels[job.experience] = (experienceLevels[job.experience] || 0) + 1;
            });

            const experienceContainer = document.getElementById('experience-filters');
            if (experienceContainer) {
                experienceContainer.innerHTML = '';
                const expOrder = ['Entry', 'Mid', 'Senior'];
                expOrder.forEach(level => {
                    if (experienceLevels[level]) {
                        const label = document.createElement('label');
                        label.className = 'flex items-center';
                        label.innerHTML = `
                            <input type="checkbox" name="experience" value="${level}" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-gray-700">${level} Level (${experienceLevels[level]})</span>
                        `;
                        experienceContainer.appendChild(label);
                    }
                });
            }

            // Generate location filters
            const locations = {};
            allJobs.forEach(job => {
                const state = job.location.split(', ')[1]; // Extract state from "City, State"
                if (state) {
                    locations[state] = (locations[state] || 0) + 1;
                }
            });

            const locationContainer = document.getElementById('location-filters');
            if (locationContainer) {
                locationContainer.innerHTML = '';
                Object.entries(locations).forEach(([state, count]) => {
                    const label = document.createElement('label');
                    label.className = 'flex items-center';
                    const stateName = {
                        'CA': 'California',
                        'WA': 'Washington',
                        'TX': 'Texas',
                        'NY': 'New York'
                    }[state] || state;
                    label.innerHTML = `
                        <input type="checkbox" name="location" value="${state}" class="rounded border-gray-300 text-primary focus:ring-primary">
                        <span class="ml-2 text-gray-700">${stateName} (${count})</span>
                    `;
                    locationContainer.appendChild(label);
                });
            }
        }

        // Initialize dynamic job system
        function initializeDynamicJobs() {
            // Override search functionality
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    filterJobs();
                });
            }

            // Override filter functionality
            const filterCheckboxes = document.querySelectorAll('input[type="checkbox"]');
            filterCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    filterJobs();
                });
            });
        }

        // Filter jobs based on search and filters
        function filterJobs() {
            const searchInput = document.querySelector('.search-input');
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const selectedFilters = {
                jobType: Array.from(document.querySelectorAll('input[name="job-type"]:checked')).map(cb => cb.value),
                experience: Array.from(document.querySelectorAll('input[name="experience"]:checked')).map(cb => cb.value),
                salary: Array.from(document.querySelectorAll('input[name="salary"]:checked')).map(cb => cb.value),
                location: Array.from(document.querySelectorAll('input[name="location"]:checked')).map(cb => cb.value)
            };

            filteredJobs = allJobs.filter(job => {
                // Search filter
                const matchesSearch = !searchTerm ||
                    job.title.toLowerCase().includes(searchTerm) ||
                    job.company.toLowerCase().includes(searchTerm) ||
                    job.skills.some(skill => skill.toLowerCase().includes(searchTerm)) ||
                    job.location.toLowerCase().includes(searchTerm);

                // Job type filter
                const matchesJobType = selectedFilters.jobType.length === 0 ||
                    selectedFilters.jobType.includes(job.type);

                // Experience filter
                const matchesExperience = selectedFilters.experience.length === 0 ||
                    selectedFilters.experience.some(exp => job.experience.includes(exp.replace('+', '')));

                // Salary filter
                const matchesSalary = selectedFilters.salary.length === 0 ||
                    selectedFilters.salary.includes(job.salaryRange);

                // Location filter
                const matchesLocation = selectedFilters.location.length === 0 ||
                    selectedFilters.location.some(loc => job.location.includes(loc));

                return matchesSearch && matchesJobType && matchesExperience && matchesSalary && matchesLocation;
            });

            currentPage = 1;
            renderJobs();
            updateJobCount();
            updatePagination();
            updateFilterCounts();
        }

        // Update filter counts based on current filtered jobs
        function updateFilterCounts() {
            // Update job type counts
            const jobTypes = {};
            filteredJobs.forEach(job => {
                jobTypes[job.type] = (jobTypes[job.type] || 0) + 1;
            });

            const jobTypeInputs = document.querySelectorAll('input[name="job-type"]');
            jobTypeInputs.forEach(input => {
                const span = input.nextElementSibling;
                const type = input.value;
                const count = jobTypes[type] || 0;
                span.textContent = `${type} (${count})`;
            });

            // Update salary range counts
            const salaryRanges = {};
            filteredJobs.forEach(job => {
                salaryRanges[job.salaryRange] = (salaryRanges[job.salaryRange] || 0) + 1;
            });

            const salaryInputs = document.querySelectorAll('input[name="salary"]');
            salaryInputs.forEach(input => {
                const span = input.nextElementSibling;
                const range = input.value;
                const count = salaryRanges[range] || 0;
                const displayText = range === '100k+' ? '$100,000+' :
                                  range.replace('k', ',000').replace('-', ' - $').replace(/^/, '$');
                span.textContent = `${displayText} (${count})`;
            });

            // Update experience level counts
            const experienceLevels = {};
            filteredJobs.forEach(job => {
                experienceLevels[job.experience] = (experienceLevels[job.experience] || 0) + 1;
            });

            const experienceInputs = document.querySelectorAll('input[name="experience"]');
            experienceInputs.forEach(input => {
                const span = input.nextElementSibling;
                const level = input.value;
                const count = experienceLevels[level] || 0;
                span.textContent = `${level} Level (${count})`;
            });

            // Update location counts
            const locations = {};
            filteredJobs.forEach(job => {
                const state = job.location.split(', ')[1];
                if (state) {
                    locations[state] = (locations[state] || 0) + 1;
                }
            });

            const locationInputs = document.querySelectorAll('input[name="location"]');
            locationInputs.forEach(input => {
                const span = input.nextElementSibling;
                const state = input.value;
                const count = locations[state] || 0;
                const stateName = {
                    'CA': 'California',
                    'WA': 'Washington',
                    'TX': 'Texas',
                    'NY': 'New York'
                }[state] || state;
                span.textContent = `${stateName} (${count})`;
            });
        }

        // Render jobs dynamically
        function renderJobs() {
            const jobContainer = document.getElementById('jobs-container');
            if (!jobContainer) {
                console.error('Job container not found!');
                return;
            }

            console.log('Rendering jobs...', filteredJobs.length, 'jobs to show');

            // Set container class based on current view
            if (currentView === 'grid') {
                jobContainer.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6';
            } else {
                jobContainer.className = 'space-y-4 xl:space-y-6 2xl:space-y-8';
            }

            const startIndex = (currentPage - 1) * jobsPerPage;
            const endIndex = startIndex + jobsPerPage;
            const jobsToShow = filteredJobs.slice(startIndex, endIndex);

            jobContainer.innerHTML = '';

            jobsToShow.forEach(job => {
                const jobCard = createJobCard(job);
                jobContainer.appendChild(jobCard);
            });

            // Reinitialize interactive elements
            initializeSkillTags();
            initializeApplyButtons();
        }

        // Create job card HTML
        function createJobCard(job) {
            const jobCard = document.createElement('div');
            const isGridView = currentView === 'grid';

            // Set classes based on view
            if (isGridView) {
                jobCard.className = 'job-card bg-white rounded-lg shadow-md hover:shadow-lg transition p-4 xl:p-6 2xl:p-8 cursor-pointer h-full';
            } else {
                jobCard.className = 'job-card bg-white rounded-lg shadow-md hover:shadow-lg transition p-6 xl:p-8 2xl:p-10 cursor-pointer';
            }

            jobCard.setAttribute('data-salary', job.salary);
            jobCard.setAttribute('data-company', job.company);
            jobCard.setAttribute('data-posted', job.posted);
            jobCard.onclick = () => window.location.href = 'job-details.html';

            // Adjust skill tags and description based on view
            const skillTagClass = isGridView ? 'px-2 py-1 text-xs xl:text-sm 2xl:text-base xl:px-3 xl:py-2' : 'px-3 py-1 text-sm xl:text-base 2xl:text-lg xl:px-4 xl:py-2';
            const descriptionClass = isGridView ? 'text-gray-700 mb-3 text-sm xl:text-base 2xl:text-lg' : 'text-gray-700 mb-4 xl:text-lg 2xl:text-xl';
            const jobInfoClass = isGridView ? 'flex flex-col space-y-1 text-xs xl:text-sm 2xl:text-base text-gray-500' : 'flex items-center space-x-4 text-sm xl:text-base 2xl:text-lg text-gray-500';

            const skillsHTML = job.skills.map(skill =>
                `<span class="skill-tag bg-blue-100 text-blue-800 ${skillTagClass} rounded-full cursor-pointer hover:bg-blue-200 transition">${skill}</span>`
            ).join('');

            const postedDate = new Date(job.posted);
            const daysAgo = Math.floor((new Date() - postedDate) / (1000 * 60 * 60 * 24));
            const postedText = daysAgo === 0 ? 'Today' : daysAgo === 1 ? '1 day ago' : `${daysAgo} days ago`;

            // Truncate description for grid view
            const description = isGridView && job.description.length > 80
                ? job.description.substring(0, 80) + '...'
                : job.description;

            jobCard.innerHTML = `
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 xl:w-16 xl:h-16 2xl:w-20 2xl:h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg xl:text-xl 2xl:text-2xl">
                            ${job.company.charAt(0)}
                        </div>
                        <div>
                            <h3 class="text-xl xl:text-2xl 2xl:text-3xl font-semibold text-gray-900 hover:text-primary transition">${job.title}</h3>
                            <p class="text-gray-600 font-medium xl:text-lg 2xl:text-xl">${job.company}</p>
                            <div class="${jobInfoClass} mt-2">
                                <span><i class="fas fa-map-marker-alt mr-1"></i>${job.location}</span>
                                <span><i class="fas fa-clock mr-1"></i>Posted ${postedText}</span>
                                <span><i class="fas fa-users mr-1"></i>${job.applicants}+ applicants</span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="text-right">
                            <div class="text-xl font-bold text-green-600">$${(job.salary / 1000).toFixed(0)}k</div>
                            <div class="text-sm text-gray-500">per year</div>
                        </div>
                        <button class="bookmark-btn p-2 text-gray-400 hover:text-yellow-500 transition" onclick="event.stopPropagation();">
                            <i class="far fa-bookmark"></i>
                        </button>
                    </div>
                </div>

                <p class="${descriptionClass}">${description}</p>

                <div class="flex flex-wrap gap-2 mb-4">
                    ${skillsHTML}
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span><i class="fas fa-briefcase mr-1"></i>${job.type}</span>
                        <span><i class="fas fa-graduation-cap mr-1"></i>${job.experience}</span>
                    </div>
                    <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition font-medium" onclick="event.stopPropagation();">
                        Apply Now
                    </button>
                </div>
            `;

            return jobCard;
        }

        // Update job count display
        function updateJobCount() {
            const jobCountElement = document.getElementById('job-count');
            if (!jobCountElement) return;

            const totalJobs = filteredJobs.length;
            const startIndex = (currentPage - 1) * jobsPerPage + 1;
            const endIndex = Math.min(currentPage * jobsPerPage, totalJobs);

            if (totalJobs === 0) {
                jobCountElement.textContent = 'No jobs found';
            } else {
                jobCountElement.textContent = `Showing ${startIndex}-${endIndex} of ${totalJobs} jobs`;
            }
        }

        // Update pagination
        function updatePagination() {
            const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
            const paginationNav = document.getElementById('pagination-nav');

            if (!paginationNav) return;

            // Clear existing pagination
            paginationNav.innerHTML = '';

            if (totalPages <= 1) {
                document.getElementById('pagination-container').style.display = 'none';
                return;
            }

            document.getElementById('pagination-container').style.display = 'flex';

            // Previous button
            const prevButton = document.createElement('button');
            prevButton.id = 'prev-page';
            prevButton.className = 'px-3 py-2 text-gray-500 hover:text-primary transition';
            prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevButton.disabled = (currentPage === 1);
            if (prevButton.disabled) {
                prevButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
            prevButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderJobs();
                    updateJobCount();
                    updatePagination();
                }
            });
            paginationNav.appendChild(prevButton);

            // Page buttons
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Adjust start page if we're near the end
            if (endPage - startPage < maxVisiblePages - 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // First page if not visible
            if (startPage > 1) {
                const firstPageBtn = createPageButton(1);
                paginationNav.appendChild(firstPageBtn);

                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-3 py-2 text-gray-500';
                    ellipsis.textContent = '...';
                    paginationNav.appendChild(ellipsis);
                }
            }

            // Visible page buttons
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = createPageButton(i);
                paginationNav.appendChild(pageBtn);
            }

            // Last page if not visible
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-3 py-2 text-gray-500';
                    ellipsis.textContent = '...';
                    paginationNav.appendChild(ellipsis);
                }

                const lastPageBtn = createPageButton(totalPages);
                paginationNav.appendChild(lastPageBtn);
            }

            // Next button
            const nextButton = document.createElement('button');
            nextButton.id = 'next-page';
            nextButton.className = 'px-3 py-2 text-gray-500 hover:text-primary transition';
            nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextButton.disabled = (currentPage === totalPages);
            if (nextButton.disabled) {
                nextButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
            nextButton.addEventListener('click', () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    renderJobs();
                    updateJobCount();
                    updatePagination();
                }
            });
            paginationNav.appendChild(nextButton);
        }

        // Create page button
        function createPageButton(pageNum) {
            const button = document.createElement('button');
            button.className = 'page-btn px-3 py-2 rounded transition';
            button.dataset.page = pageNum;
            button.textContent = pageNum;

            if (pageNum === currentPage) {
                button.classList.add('bg-primary', 'text-white');
            } else {
                button.classList.add('text-gray-700', 'hover:text-primary', 'hover:bg-gray-100');
            }

            button.addEventListener('click', () => {
                currentPage = pageNum;
                renderJobs();
                updateJobCount();
                updatePagination();

                // Scroll to top of job results
                document.getElementById('jobs-container').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });

            return button;
        }

        // Skill tag filtering
        function initializeSkillTags() {
            const skillTags = document.querySelectorAll('.skill-tag');

            skillTags.forEach(tag => {
                tag.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const skill = this.textContent.trim();

                    // Update search input with skill
                    const searchInput = document.querySelector('.search-input');
                    if (searchInput) {
                        searchInput.value = skill;
                        filterJobs();
                    }

                    EasyNaukri.showNotification(`Searching for jobs with "${skill}" skill...`, 'info');
                });
            });
        }

        // Apply button functionality
        function initializeApplyButtons() {
            // Remove existing event listeners to prevent duplicates
            document.querySelectorAll('button').forEach(button => {
                if (button.textContent.includes('Apply Now')) {
                    // Clone button to remove all event listeners
                    const newButton = button.cloneNode(true);
                    button.parentNode.replaceChild(newButton, button);

                    // Add new event listener
                    newButton.addEventListener('click', function(e) {
                        e.stopPropagation(); // Prevent job card click

                        // Get job details from the card
                        const jobCard = this.closest('.bg-white');
                        const jobTitle = jobCard.querySelector('h3').textContent;
                        const company = jobCard.querySelector('p').textContent;

                        // Show loading state
                        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Applying...';
                        this.disabled = true;

                        EasyNaukri.showNotification(`Preparing application for ${jobTitle} at ${company}...`, 'info');

                        // Simulate application process
                        setTimeout(() => {
                            // Create application form modal or redirect to application page
                            showApplicationModal(jobTitle, company);

                            // Reset button
                            this.innerHTML = 'Apply Now';
                            this.disabled = false;
                        }, 1500);
                    });
                }
            });
        }

        // Show application form modal
        function showApplicationModal(jobTitle, company) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto';
            modal.innerHTML = `
                <div class="bg-white rounded-lg max-w-2xl w-full p-6 my-8">
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Apply for Position</h3>
                            <p class="text-gray-600">${jobTitle} at ${company}</p>
                        </div>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 text-2xl">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="applicationForm" class="space-y-6">
                        <input type="hidden" name="jobTitle" value="${jobTitle}">
                        <input type="hidden" name="company" value="${company}">

                        <!-- Personal Information -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h4>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                    <input type="text" name="fullName" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                    <input type="email" name="email" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                    <input type="text" name="location" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="City, State">
                                </div>
                            </div>
                        </div>

                        <!-- Experience & Skills -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Experience & Skills</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Years of Experience</label>
                                    <select name="experience" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                        <option value="">Select Experience</option>
                                        <option value="0-1">0-1 years</option>
                                        <option value="2-3">2-3 years</option>
                                        <option value="4-5">4-5 years</option>
                                        <option value="6-10">6-10 years</option>
                                        <option value="10+">10+ years</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Key Skills</label>
                                    <textarea name="skills" rows="3" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="List your relevant skills..."></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Current/Previous Job Title</label>
                                    <input type="text" name="currentJob" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="e.g. Software Developer">
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">Additional Information</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Why are you interested in this position?</label>
                                    <textarea name="interest" rows="3" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Tell us what interests you about this role..."></textarea>
                                </div>
                                <div class="grid md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Expected Salary</label>
                                        <input type="text" name="expectedSalary" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="e.g. $80,000">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Notice Period</label>
                                        <select name="noticePeriod" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <option value="">Select Notice Period</option>
                                            <option value="immediate">Immediate</option>
                                            <option value="2weeks">2 weeks</option>
                                            <option value="1month">1 month</option>
                                            <option value="2months">2 months</option>
                                            <option value="3months">3 months</option>
                                        </select>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Resume/CV Link or Summary</label>
                                    <textarea name="resume" rows="4" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Paste your resume link (Google Drive, LinkedIn, etc.) or provide a brief summary of your background..."></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Additional Comments</label>
                                    <textarea name="comments" rows="2" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Any additional information..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end space-x-4 pt-4 border-t">
                            <button type="button" onclick="this.closest('.fixed').remove()" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                                Cancel
                            </button>
                            <button type="submit" class="px-8 py-3 bg-primary text-white rounded-lg hover:bg-secondary transition font-medium">
                                Submit Application
                            </button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // Handle form submission
            const form = modal.querySelector('#applicationForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                handleApplicationSubmission(this);
            });

            // Close modal when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // Handle application form submission
        function handleApplicationSubmission(form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting...';
            submitBtn.disabled = true;

            // Collect form data
            const formData = new FormData(form);
            const applicationData = {};

            for (let [key, value] of formData.entries()) {
                applicationData[key] = value;
            }

            // Add timestamp
            applicationData.submittedAt = new Date().toLocaleString();

            // Send email using EmailJS (you'll need to set this up)
            sendApplicationEmail(applicationData)
                .then(() => {
                    // Show success message
                    EasyNaukri.showNotification('Application submitted successfully! You will receive a confirmation email shortly.', 'success');

                    // Close modal after delay
                    setTimeout(() => {
                        form.closest('.fixed').remove();
                    }, 2000);
                })
                .catch((error) => {
                    console.error('Error sending application:', error);
                    EasyNaukri.showNotification('There was an error submitting your application. Please try again.', 'error');

                    // Reset button
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
        }

        // Send application email using EmailJS
        function sendApplicationEmail(applicationData) {
            return new Promise((resolve, reject) => {
                // ADMIN EMAIL CONFIGURATION
                // Replace '<EMAIL>' with your actual admin email address
                const ADMIN_EMAIL = '<EMAIL>';

                // Email sending functionality - ready for EmailJS integration

                console.log('Application Data to be sent to admin:', applicationData);
                console.log('Admin Email:', ADMIN_EMAIL);

                // Simulate API call delay
                setTimeout(() => {
                    // Create email content
                    const emailContent = createEmailContent(applicationData);

                    // In a real implementation, you would use EmailJS like this:
                    /*
                    emailjs.send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', {
                        to_email: '<EMAIL>',
                        subject: `New Job Application: ${applicationData.jobTitle} at ${applicationData.company}`,
                        message: emailContent,
                        applicant_name: applicationData.fullName,
                        applicant_email: applicationData.email,
                        job_title: applicationData.jobTitle,
                        company: applicationData.company
                    }).then(resolve).catch(reject);
                    */

                    // Application submitted successfully
                    resolve();
                }, 1500);
            });
        }

        // Create formatted email content
        function createEmailContent(data) {
            return `
New Job Application Received

Job Details:
- Position: ${data.jobTitle}
- Company: ${data.company}
- Submitted: ${data.submittedAt}

Applicant Information:
- Name: ${data.fullName}
- Email: ${data.email}
- Phone: ${data.phone}
- Location: ${data.location || 'Not specified'}

Experience & Skills:
- Years of Experience: ${data.experience || 'Not specified'}
- Current/Previous Job: ${data.currentJob || 'Not specified'}
- Key Skills: ${data.skills || 'Not specified'}

Additional Information:
- Interest in Position: ${data.interest || 'Not specified'}
- Expected Salary: ${data.expectedSalary || 'Not specified'}
- Notice Period: ${data.noticePeriod || 'Not specified'}
- Resume/CV: ${data.resume || 'Not provided'}
- Additional Comments: ${data.comments || 'None'}

Please review this application and contact the candidate if suitable.
            `.trim();
        }

        // Sort functionality
        function initializeSortBy() {
            const sortSelect = document.getElementById('sort-select');

            if (!sortSelect) return;

            sortSelect.addEventListener('change', function() {
                const sortValue = this.value;

                // Show sorting notification
                EasyNaukri.showNotification(`Sorting jobs by ${this.options[this.selectedIndex].text.replace('Sort by: ', '')}...`, 'info');

                // Sort the filtered jobs array
                filteredJobs.sort((a, b) => {
                    switch(sortValue) {
                        case 'date':
                            // Sort by date (newest first)
                            const dateA = new Date(a.posted);
                            const dateB = new Date(b.posted);
                            return dateB - dateA;

                        case 'salary-high':
                            // Sort by salary (high to low)
                            return b.salary - a.salary;

                        case 'salary-low':
                            // Sort by salary (low to high)
                            return a.salary - b.salary;

                        case 'company':
                            // Sort by company name
                            return a.company.localeCompare(b.company);

                        case 'relevance':
                        default:
                            // Restore original order by ID
                            return a.id - b.id;
                    }
                });

                // Re-render jobs with new sort order
                currentPage = 1;
                renderJobs();
                updateJobCount();
                updatePagination();
            });
        }

        // Helper function to extract salary number for sorting
        function extractSalary(salaryText) {
            const match = salaryText.match(/\$([0-9,]+)/);
            if (match) {
                return parseInt(match[1].replace(/,/g, ''));
            }
            return 0;
        }

        // View toggle functionality
        function initializeViewToggle() {
            const gridViewBtn = document.getElementById('grid-view-btn');
            const listViewBtn = document.getElementById('list-view-btn');

            if (!gridViewBtn || !listViewBtn) return;

            // List View (default)
            listViewBtn.addEventListener('click', function() {
                // Update button states
                listViewBtn.classList.add('text-primary');
                listViewBtn.classList.remove('text-gray-400');
                gridViewBtn.classList.add('text-gray-400');
                gridViewBtn.classList.remove('text-primary');

                currentView = 'list';
                renderJobs();

                EasyNaukri.showNotification('Switched to List View', 'success');
            });

            // Grid View
            gridViewBtn.addEventListener('click', function() {
                // Update button states
                gridViewBtn.classList.add('text-primary');
                gridViewBtn.classList.remove('text-gray-400');
                listViewBtn.classList.add('text-gray-400');
                listViewBtn.classList.remove('text-primary');

                currentView = 'grid';
                renderJobs();

                EasyNaukri.showNotification('Switched to Grid View', 'success');
            });
        }

        // Pagination functionality
        function initializePagination() {
            const pageButtons = document.querySelectorAll('.page-btn');
            const prevButton = document.getElementById('prev-page');
            const nextButton = document.getElementById('next-page');

            // Page button clicks
            pageButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const page = parseInt(this.dataset.page);
                    goToPage(page);
                });
            });

            // Previous button
            prevButton.addEventListener('click', function() {
                const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
                if (currentPage > 1) {
                    goToPage(currentPage - 1);
                }
            });

            // Next button
            nextButton.addEventListener('click', function() {
                const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
                if (currentPage < totalPages) {
                    goToPage(currentPage + 1);
                }
            });

            function goToPage(page) {
                currentPage = page;

                // Show notification
                EasyNaukri.showNotification(`Loading page ${page}...`, 'info');

                // Re-render jobs for new page
                renderJobs();
                updateJobCount();
                updatePagination();

                // Scroll to top of job listings
                setTimeout(() => {
                    document.getElementById('jobs-container').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    EasyNaukri.showNotification(`Page ${page} loaded successfully`, 'success');
                }, 300);
            }
        }



        // Handle window resize for responsive layout
        function handleResize() {
            // Re-render jobs to apply correct grid classes for current screen size
            if (typeof renderJobs === 'function') {
                renderJobs();
            }
        }

        // Add resize listener
        window.addEventListener('resize', debounce(handleResize, 250));

        // Debounce function to limit resize event frequency
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
