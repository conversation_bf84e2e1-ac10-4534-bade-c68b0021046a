<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Builder - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-primary font-medium">Resume Builder</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-primary transition flex items-center">
                            For Employers <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="login-dashboard.html?redirect=dashboard/employer.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-t-lg">Dashboard</a>
                            <a href="post-job.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-b-lg">Post a Job</a>
                        </div>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-primary transition">Sign In</a>
                    <a href="signup.html" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition">Sign Up</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-gray-700">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-primary font-medium">Resume Builder</a>
                <a href="dashboard/employer.html" class="block py-2 text-gray-700">For Employers</a>
                <div class="border-t pt-2 mt-2">
                    <a href="login.html" class="block py-2 text-gray-700">Sign In</a>
                    <a href="signup.html" class="block py-2 text-primary font-medium">Sign Up</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="bg-gradient-to-r from-primary to-secondary text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold mb-4">Professional Resume Builder</h1>
            <p class="text-xl text-blue-100 mb-6">Create a stunning resume in minutes with our easy-to-use builder</p>
            <div class="flex items-center justify-center space-x-8 text-blue-100">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>ATS-Friendly Templates</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Professional Designs</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Easy Export</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Resume Builder -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Form Section -->
            <div class="lg:w-1/2">
                <!-- Progress Bar -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-gray-900">Resume Progress</h3>
                        <span class="text-sm text-gray-600" id="progress-text">0% Complete</span>
                    </div>
                    <div class="bg-gray-200 rounded-full h-2">
                        <div class="bg-primary h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Resume Form -->
                <div class="bg-white rounded-lg shadow-md">
                    <!-- Form Tabs -->
                    <div class="border-b">
                        <nav class="flex space-x-8 px-6">
                            <button class="tab-btn py-4 border-b-2 border-primary text-primary font-medium" data-tab="personal">
                                <i class="fas fa-user mr-2"></i>Personal Info
                            </button>
                            <button class="tab-btn py-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium" data-tab="experience">
                                <i class="fas fa-briefcase mr-2"></i>Experience
                            </button>
                            <button class="tab-btn py-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium" data-tab="education">
                                <i class="fas fa-graduation-cap mr-2"></i>Education
                            </button>
                            <button class="tab-btn py-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium" data-tab="skills">
                                <i class="fas fa-cogs mr-2"></i>Skills
                            </button>
                        </nav>
                    </div>

                    <!-- Tab Content -->
                    <div class="p-6">
                        <!-- Personal Info Tab -->
                        <div id="personal" class="tab-content">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                            <form class="space-y-4">
                                <div class="grid md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="John">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Doe">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Professional Title</label>
                                    <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Senior Software Engineer">
                                </div>
                                <div class="grid md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                        <input type="email" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="<EMAIL>">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                        <input type="tel" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="+****************">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                    <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="San Francisco, CA">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">LinkedIn Profile</label>
                                    <input type="url" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://linkedin.com/in/johndoe">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Professional Summary</label>
                                    <textarea rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Write a brief summary of your professional background and key achievements..."></textarea>
                                </div>
                            </form>
                        </div>

                        <!-- Experience Tab -->
                        <div id="experience" class="tab-content hidden">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Work Experience</h3>
                                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition text-sm">
                                    <i class="fas fa-plus mr-2"></i>Add Experience
                                </button>
                            </div>
                            
                            <!-- Experience Entry -->
                            <div class="border border-gray-200 rounded-lg p-4 mb-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-medium text-gray-900">Experience #1</h4>
                                    <button class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <form class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Job Title</label>
                                        <input type="text" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Senior Software Engineer">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Google Inc.">
                                    </div>
                                    <div class="grid md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                                            <input type="month" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                                            <input type="month" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <label class="flex items-center mt-2">
                                                <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
                                                <span class="ml-2 text-sm text-gray-700">Currently working here</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Mountain View, CA">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Job Description</label>
                                        <textarea rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Describe your responsibilities and achievements..."></textarea>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Education Tab -->
                        <div id="education" class="tab-content hidden">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Education</h3>
                                <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition text-sm">
                                    <i class="fas fa-plus mr-2"></i>Add Education
                                </button>
                            </div>
                            
                            <!-- Education Entry -->
                            <div class="border border-gray-200 rounded-lg p-4 mb-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-medium text-gray-900">Education #1</h4>
                                    <button class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                                <form class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Degree</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Bachelor of Science in Computer Science">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">School/University</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Stanford University">
                                    </div>
                                    <div class="grid md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Start Year</label>
                                            <input type="number" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="2018">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">End Year</label>
                                            <input type="number" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="2022">
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">GPA (Optional)</label>
                                        <input type="text" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="3.8/4.0">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Relevant Coursework (Optional)</label>
                                        <textarea rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Data Structures, Algorithms, Software Engineering..."></textarea>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Skills Tab -->
                        <div id="skills" class="tab-content hidden">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Skills & Technologies</h3>
                            <form class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Technical Skills</label>
                                    <div class="flex flex-wrap gap-2 mb-3" id="technical-skills">
                                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
                                            JavaScript <button type="button" class="ml-2 text-blue-600 hover:text-blue-800">×</button>
                                        </span>
                                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
                                            React <button type="button" class="ml-2 text-blue-600 hover:text-blue-800">×</button>
                                        </span>
                                        <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center">
                                            Node.js <button type="button" class="ml-2 text-blue-600 hover:text-blue-800">×</button>
                                        </span>
                                    </div>
                                    <div class="flex">
                                        <input type="text" id="skill-input" class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Add a skill">
                                        <button type="button" id="add-skill-btn" class="bg-primary text-white px-4 py-2 rounded-r-lg hover:bg-secondary transition">Add</button>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Languages</label>
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-4">
                                            <input type="text" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Language" value="English">
                                            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                                <option>Native</option>
                                                <option>Fluent</option>
                                                <option>Intermediate</option>
                                                <option>Basic</option>
                                            </select>
                                            <button type="button" class="text-red-500 hover:text-red-700">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <button type="button" class="text-primary hover:underline text-sm">
                                            <i class="fas fa-plus mr-1"></i>Add Language
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Certifications (Optional)</label>
                                    <textarea rows="3" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="List your relevant certifications..."></textarea>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="border-t px-6 py-4">
                        <div class="flex items-center justify-between">
                            <button class="text-gray-600 hover:text-gray-800 transition" id="save-draft-btn">
                                <i class="fas fa-save mr-2"></i>Save Draft
                            </button>
                            <div class="flex items-center space-x-3">
                                <!-- Previous Button (hidden on first tab) -->
                                <button class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition hidden" id="prev-btn">
                                    <i class="fas fa-arrow-left mr-2"></i>Previous
                                </button>
                                <!-- Next Button (shown on first 3 tabs) -->
                                <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition" id="next-btn">
                                    Next<i class="fas fa-arrow-right ml-2"></i>
                                </button>
                                <!-- Final Actions (shown only on last tab) -->
                                <div class="hidden space-x-3" id="final-actions">
                                    <button class="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-300 transition" id="preview-btn">
                                        Preview
                                    </button>
                                    <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition" id="download-btn">
                                        <i class="fas fa-download mr-2"></i>Download PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="lg:w-1/2">
                <div class="sticky top-24">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-900">Resume Preview</h3>
                            <div class="flex space-x-2">
                                <button class="p-2 text-gray-600 hover:text-gray-800 transition" title="Zoom Out">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button class="p-2 text-gray-600 hover:text-gray-800 transition" title="Zoom In">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Resume Preview -->
                        <div class="border border-gray-300 rounded-lg p-6 bg-white" style="min-height: 600px; transform: scale(0.8); transform-origin: top left;">
                            <!-- Resume Header -->
                            <div class="text-center mb-6">
                                <h1 class="text-2xl font-bold text-gray-900 mb-1">John Doe</h1>
                                <p class="text-lg text-gray-600 mb-2">Senior Software Engineer</p>
                                <div class="flex items-center justify-center space-x-4 text-sm text-gray-600">
                                    <span><i class="fas fa-envelope mr-1"></i><EMAIL></span>
                                    <span><i class="fas fa-phone mr-1"></i>+****************</span>
                                    <span><i class="fas fa-map-marker-alt mr-1"></i>San Francisco, CA</span>
                                </div>
                            </div>

                            <!-- Professional Summary -->
                            <div class="mb-6">
                                <h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Professional Summary</h2>
                                <p class="text-gray-700 text-sm leading-relaxed">
                                    Experienced software engineer with 5+ years of expertise in full-stack development, 
                                    specializing in React, Node.js, and cloud technologies. Proven track record of 
                                    delivering scalable solutions and leading cross-functional teams.
                                </p>
                            </div>

                            <!-- Experience -->
                            <div class="mb-6">
                                <h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Experience</h2>
                                <div class="mb-4">
                                    <div class="flex justify-between items-start mb-1">
                                        <h3 class="font-medium text-gray-900">Senior Software Engineer</h3>
                                        <span class="text-sm text-gray-600">2021 - Present</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">Google Inc. • Mountain View, CA</p>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• Led development of key features serving 10M+ users</li>
                                        <li>• Improved application performance by 40% through optimization</li>
                                        <li>• Mentored junior developers and conducted code reviews</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Education -->
                            <div class="mb-6">
                                <h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Education</h2>
                                <div>
                                    <div class="flex justify-between items-start mb-1">
                                        <h3 class="font-medium text-gray-900">Bachelor of Science in Computer Science</h3>
                                        <span class="text-sm text-gray-600">2018 - 2022</span>
                                    </div>
                                    <p class="text-sm text-gray-600">Stanford University • GPA: 3.8/4.0</p>
                                </div>
                            </div>

                            <!-- Skills -->
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-1 mb-3">Skills</h2>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">JavaScript</span>
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">React</span>
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">Node.js</span>
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">Python</span>
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">AWS</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities across the United States.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <li><a href="dashboard/candidate.html" class="hover:text-white transition">My Dashboard</a></li>
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-material.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="dashboard/employer.html" class="hover:text-white transition">Employer Dashboard</a></li>
                        <li><a href="post-job.html" class="hover:text-white transition">Post a Job</a></li>
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help-center.html" class="hover:text-white transition">Help Center</a></li>
                        <!-- <li><a href="help-center.html" class="hover:text-white transition">Contact Us</a></li> -->
                        <li><a href="privacy-policy.html" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tabs with custom navigation
            initializeResumeBuilder();

            // Skill management
            const skillInput = document.getElementById('skill-input');
            const addSkillBtn = document.getElementById('add-skill-btn');
            const skillsContainer = document.getElementById('technical-skills');
            
            function addSkill() {
                const skillText = skillInput.value.trim();
                if (skillText) {
                    const skillTag = document.createElement('span');
                    skillTag.className = 'bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center';
                    skillTag.innerHTML = `${skillText} <button type="button" class="ml-2 text-blue-600 hover:text-blue-800">×</button>`;
                    
                    skillTag.querySelector('button').addEventListener('click', function() {
                        skillTag.remove();
                    });
                    
                    skillsContainer.appendChild(skillTag);
                    skillInput.value = '';
                }
            }
            
            addSkillBtn.addEventListener('click', addSkill);
            skillInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addSkill();
                }
            });
            
            // Remove skill functionality for existing skills
            document.querySelectorAll('#technical-skills button').forEach(button => {
                button.addEventListener('click', function() {
                    this.parentElement.remove();
                });
            });

            // Save draft functionality
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                const icon = button.querySelector('i');
                if (icon && icon.classList.contains('fa-save')) {
                    button.addEventListener('click', function() {
                        EasyNaukri.showNotification('Resume draft saved successfully!', 'success');
                    });
                } else if (icon && icon.classList.contains('fa-download')) {
                    button.addEventListener('click', function() {
                        EasyNaukri.showNotification('PDF download feature coming soon!', 'info');
                    });
                } else if (icon && icon.classList.contains('fa-eye')) {
                    button.addEventListener('click', function() {
                        EasyNaukri.showNotification('Preview updated!', 'success');
                    });
                }
            });

            // Real-time preview updates
            const personalForm = document.querySelector('#personal form');
            if (personalForm) {
                const inputs = personalForm.querySelectorAll('input, textarea');
                inputs.forEach(input => {
                    input.addEventListener('input', function() {
                        updatePreview();
                    });
                });
            }

            function updatePreview() {
                const firstName = document.querySelector('#personal input[placeholder="John"]')?.value || 'John';
                const lastName = document.querySelector('#personal input[placeholder="Doe"]')?.value || 'Doe';
                const title = document.querySelector('#personal input[placeholder="Senior Software Engineer"]')?.value || 'Senior Software Engineer';

                // Update preview
                const previewName = document.querySelector('.lg\\:w-1\\/2 h1');
                const previewTitle = document.querySelector('.lg\\:w-1\\/2 p.text-lg');

                if (previewName) previewName.textContent = `${firstName} ${lastName}`;
                if (previewTitle) previewTitle.textContent = title;
            }

            // Resume Builder Navigation System
            function initializeResumeBuilder() {
                const tabs = ['personal', 'experience', 'education', 'skills'];
                let currentTabIndex = 0;
                let completedTabs = []; // Track which tabs are completed

                const tabButtons = document.querySelectorAll('.tab-btn');
                const tabContents = document.querySelectorAll('.tab-content');
                const nextBtn = document.getElementById('next-btn');
                const prevBtn = document.getElementById('prev-btn');
                const finalActions = document.getElementById('final-actions');
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');

                // Update progress bar based on completed tabs
                function updateProgress() {
                    const progress = (completedTabs.length / tabs.length) * 100;
                    progressBar.style.width = progress + '%';
                    progressText.textContent = Math.round(progress) + '% Complete';
                }

                // Show specific tab
                function showTab(index) {
                    // Hide all tabs
                    tabContents.forEach(content => content.classList.add('hidden'));
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-primary', 'text-primary');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });

                    // Show current tab
                    const targetTab = tabs[index];
                    const targetContent = document.getElementById(targetTab);
                    const targetButton = document.querySelector(`[data-tab="${targetTab}"]`);

                    if (targetContent) targetContent.classList.remove('hidden');
                    if (targetButton) {
                        targetButton.classList.add('border-primary', 'text-primary');
                        targetButton.classList.remove('border-transparent', 'text-gray-500');
                    }

                    // Update navigation buttons
                    prevBtn.classList.toggle('hidden', index === 0);
                    nextBtn.classList.toggle('hidden', index === tabs.length - 1);
                    finalActions.classList.toggle('hidden', index !== tabs.length - 1);
                    finalActions.classList.toggle('flex', index === tabs.length - 1);

                    // Don't update progress just by switching tabs
                }

                // Tab button clicks - only allow navigation to completed tabs or current tab
                tabButtons.forEach((button, index) => {
                    button.addEventListener('click', function() {
                        const targetTab = tabs[index];

                        // Allow navigation to:
                        // 1. Current tab
                        // 2. Completed tabs
                        // 3. First tab (always accessible)
                        if (index === currentTabIndex || completedTabs.includes(targetTab) || index === 0) {
                            currentTabIndex = index;
                            showTab(currentTabIndex);
                        } else {
                            EasyNaukri.showNotification('Please complete the previous sections first.', 'warning');
                        }
                    });
                });

                // Next button with validation
                nextBtn.addEventListener('click', function() {
                    if (validateCurrentTab()) {
                        // Mark current tab as completed
                        const currentTab = tabs[currentTabIndex];
                        if (!completedTabs.includes(currentTab)) {
                            completedTabs.push(currentTab);
                            updateProgress();

                            // Add visual indicator for completed tab
                            const completedButton = document.querySelector(`[data-tab="${currentTab}"]`);
                            if (completedButton) {
                                const icon = completedButton.querySelector('i');
                                if (icon && !completedButton.querySelector('.fa-check')) {
                                    const checkIcon = document.createElement('i');
                                    checkIcon.className = 'fas fa-check ml-1 text-green-500';
                                    completedButton.appendChild(checkIcon);
                                }
                            }

                            EasyNaukri.showNotification(`${currentTab.charAt(0).toUpperCase() + currentTab.slice(1)} section completed!`, 'success');
                        }

                        if (currentTabIndex < tabs.length - 1) {
                            currentTabIndex++;
                            showTab(currentTabIndex);
                        }
                    }
                });

                // Validate current tab
                function validateCurrentTab() {
                    const currentTab = tabs[currentTabIndex];
                    const currentForm = document.querySelector(`#${currentTab} form`);

                    if (currentForm) {
                        const requiredFields = currentForm.querySelectorAll('input[required], textarea[required]');
                        let isValid = true;

                        requiredFields.forEach(field => {
                            if (!field.value.trim()) {
                                field.classList.add('border-red-500');
                                isValid = false;
                            } else {
                                field.classList.remove('border-red-500');
                            }
                        });

                        if (!isValid) {
                            EasyNaukri.showNotification('Please fill all required fields before proceeding.', 'error');
                            return false;
                        }
                    }

                    return true;
                }

                // Previous button
                prevBtn.addEventListener('click', function() {
                    if (currentTabIndex > 0) {
                        currentTabIndex--;
                        showTab(currentTabIndex);
                    }
                });

                // Initialize first tab
                showTab(0);
            }
        });
    </script>
</body>
</html>
