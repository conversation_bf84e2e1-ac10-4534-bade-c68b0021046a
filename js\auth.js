/**
 * Frontend Authentication System
 * Simple client-side authentication for dashboard protection
 */

class DashboardAuth {
    constructor() {
        this.sessionKey = 'dashboardAuth';
        this.loginUrl = '../login-dashboard.html';
        this.credentials = {
            'admin': '123456'
        };
    }

    /**
     * Check if user is authenticated
     * @returns {boolean}
     */
    isAuthenticated() {
        const authData = this.getAuthData();
        if (!authData) return false;
        
        const now = new Date().getTime();
        const isExpired = now > (authData.loginTime + authData.expiresIn);
        
        if (isExpired) {
            this.clearAuthentication();
            return false;
        }
        
        return true;
    }

    /**
     * Get stored authentication data
     * @returns {Object|null}
     */
    getAuthData() {
        try {
            const data = localStorage.getItem(this.sessionKey);
            return data ? JSON.parse(data) : null;
        } catch (e) {
            console.error('Error parsing auth data:', e);
            return null;
        }
    }

    /**
     * Store authentication data
     * @param {string} username 
     */
    storeAuthentication(username) {
        const authData = {
            username: username,
            loginTime: new Date().getTime(),
            expiresIn: 24 * 60 * 60 * 1000 // 24 hours
        };
        localStorage.setItem(this.sessionKey, JSON.stringify(authData));
    }

    /**
     * Clear authentication data
     */
    clearAuthentication() {
        localStorage.removeItem(this.sessionKey);
    }

    /**
     * Authenticate user with credentials
     * @param {string} username 
     * @param {string} password 
     * @returns {boolean}
     */
    authenticate(username, password) {
        return this.credentials[username] === password;
    }

    /**
     * Redirect to login page with current page as redirect parameter
     */
    redirectToLogin() {
        const currentPage = window.location.pathname;
        const redirectUrl = `${this.loginUrl}?redirect=${encodeURIComponent(currentPage)}`;
        window.location.href = redirectUrl;
    }

    /**
     * Protect current page - redirect to login if not authenticated
     */
    protectPage() {
        if (!this.isAuthenticated()) {
            this.redirectToLogin();
            return false;
        }
        return true;
    }

    /**
     * Get current user info
     * @returns {Object|null}
     */
    getCurrentUser() {
        const authData = this.getAuthData();
        return authData ? { username: authData.username, loginTime: authData.loginTime } : null;
    }

    /**
     * Logout user
     */
    logout() {
        this.clearAuthentication();
        this.redirectToLogin();
    }

    /**
     * Initialize authentication system
     * - Protect page if needed
     * - Set up logout handlers
     * - Display user info
     */
    init(options = {}) {
        const { 
            autoProtect = true, 
            showUserInfo = true, 
            logoutButtonId = 'logoutBtn',
            userInfoId = 'userInfo'
        } = options;

        // Auto-protect page
        if (autoProtect && !this.protectPage()) {
            return;
        }

        // Show user info
        if (showUserInfo) {
            this.displayUserInfo(userInfoId);
        }

        // Set up logout button
        this.setupLogoutButton(logoutButtonId);

        // Set up session timeout warning
        this.setupSessionTimeout();
    }

    /**
     * Display user information
     * @param {string} elementId 
     */
    displayUserInfo(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const user = this.getCurrentUser();
        if (user) {
            const loginDate = new Date(user.loginTime).toLocaleString();
            element.innerHTML = `
                <div class="flex items-center space-x-2 text-sm text-gray-600">
                    <i class="fas fa-user-circle"></i>
                    <span>Welcome, <strong>${user.username}</strong></span>
                    <span class="text-xs text-gray-400">• Logged in: ${loginDate}</span>
                </div>
            `;
        }
    }

    /**
     * Setup logout button functionality
     * @param {string} buttonId 
     */
    setupLogoutButton(buttonId) {
        const logoutBtn = document.getElementById(buttonId);
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Show confirmation
                if (confirm('Are you sure you want to logout?')) {
                    this.logout();
                }
            });
        }
    }

    /**
     * Setup session timeout warning
     */
    setupSessionTimeout() {
        const authData = this.getAuthData();
        if (!authData) return;

        const timeLeft = (authData.loginTime + authData.expiresIn) - new Date().getTime();
        const warningTime = 5 * 60 * 1000; // 5 minutes before expiry

        if (timeLeft > warningTime) {
            setTimeout(() => {
                this.showSessionWarning();
            }, timeLeft - warningTime);
        } else if (timeLeft > 0) {
            // Already in warning period
            this.showSessionWarning();
        }
    }

    /**
     * Show session timeout warning
     */
    showSessionWarning() {
        const authData = this.getAuthData();
        if (!authData) return;

        const timeLeft = Math.max(0, (authData.loginTime + authData.expiresIn) - new Date().getTime());
        const minutesLeft = Math.ceil(timeLeft / (60 * 1000));

        if (minutesLeft <= 0) {
            alert('Your session has expired. You will be redirected to the login page.');
            this.logout();
            return;
        }

        const extendSession = confirm(
            `Your session will expire in ${minutesLeft} minute(s). Would you like to extend your session?`
        );

        if (extendSession) {
            // Extend session by updating login time
            const user = this.getCurrentUser();
            this.storeAuthentication(user.username);
            
            // Show success message
            this.showNotification('Session extended successfully!', 'success');
            
            // Setup next warning
            this.setupSessionTimeout();
        }
    }

    /**
     * Show notification message
     * @param {string} message 
     * @param {string} type 
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                }"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Create global instance
window.DashboardAuth = new DashboardAuth();

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only auto-protect if we're on a dashboard page
    const isDashboardPage = window.location.pathname.includes('dashboard/') || 
                           window.location.pathname.includes('dashboard.html');
    
    if (isDashboardPage) {
        window.DashboardAuth.init({
            autoProtect: true,
            showUserInfo: true,
            logoutButtonId: 'logoutBtn',
            userInfoId: 'userInfo'
        });
    }
});
