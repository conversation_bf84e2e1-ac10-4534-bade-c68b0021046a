<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Post a Job - USA.EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/auth.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <div class="relative group">
                        <button class="text-primary font-medium flex items-center">
                            For Employers <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="login-dashboard.html?redirect=dashboard/employer.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-t-lg">Dashboard</a>
                            <a href="post-job.html" class="block px-4 py-3 text-primary font-medium rounded-b-lg">Post a Job</a>
                        </div>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-primary transition">Sign In</a>
                    <a href="signup.html" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">Sign Up</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
                <div class="px-4 py-2 space-y-2">
                    <a href="index.html" class="block py-2 text-gray-700">Home</a>
                    <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                    <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                    <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
                    <a href="dashboard/employer.html" class="block py-2 text-gray-700">For Employers</a>
                    <div class="border-t pt-2 mt-2">
                        <a href="login.html" class="block py-2 text-gray-700">Sign In</a>
                        <a href="signup.html" class="block py-2 text-primary font-medium">Sign Up</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-primary to-secondary text-white py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold mb-4">Post a Job Opening</h1>
            <p class="text-xl opacity-90">Find the perfect candidates for your company</p>
        </div>
    </div>

    <!-- Job Posting Form -->
    <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <form id="jobPostForm" class="space-y-8">
                <!-- Company Information -->
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Company Information</h2>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Name *</label>
                            <input type="text" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Enter company name">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Website</label>
                            <input type="url" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://www.company.com">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Industry *</label>
                            <select required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">Select Industry</option>
                                <option value="technology">Technology</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Finance</option>
                                <option value="education">Education</option>
                                <option value="retail">Retail</option>
                                <option value="manufacturing">Manufacturing</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Size</label>
                            <select class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">Select Size</option>
                                <option value="1-10">1-10 employees</option>
                                <option value="11-50">11-50 employees</option>
                                <option value="51-200">51-200 employees</option>
                                <option value="201-500">201-500 employees</option>
                                <option value="500+">500+ employees</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Job Details -->
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Job Details</h2>
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Job Title *</label>
                            <input type="text" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="e.g. Senior Software Engineer">
                        </div>
                        
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Job Category *</label>
                                <select required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Category</option>
                                    <option value="software-development">Software Development</option>
                                    <option value="data-science">Data Science</option>
                                    <option value="design">Design</option>
                                    <option value="marketing">Marketing</option>
                                    <option value="sales">Sales</option>
                                    <option value="hr">Human Resources</option>
                                    <option value="finance">Finance</option>
                                    <option value="operations">Operations</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Employment Type *</label>
                                <select required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Type</option>
                                    <option value="full-time">Full-time</option>
                                    <option value="part-time">Part-time</option>
                                    <option value="contract">Contract</option>
                                    <option value="freelance">Freelance</option>
                                    <option value="internship">Internship</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Experience Level *</label>
                                <select required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Level</option>
                                    <option value="entry">Entry Level (0-2 years)</option>
                                    <option value="mid">Mid Level (3-5 years)</option>
                                    <option value="senior">Senior Level (6-10 years)</option>
                                    <option value="lead">Lead/Principal (10+ years)</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Work Location *</label>
                                <select required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Location</option>
                                    <option value="remote">Remote</option>
                                    <option value="hybrid">Hybrid</option>
                                    <option value="onsite">On-site</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Location/City *</label>
                            <input type="text" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="e.g. New York, NY">
                        </div>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Salary Range (USD)</label>
                                <div class="flex space-x-2">
                                    <input type="number" class="flex-1 border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Min">
                                    <span class="flex items-center text-gray-500">to</span>
                                    <input type="number" class="flex-1 border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Max">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Salary Period</label>
                                <select class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="yearly">Per Year</option>
                                    <option value="monthly">Per Month</option>
                                    <option value="hourly">Per Hour</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Job Description -->
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Job Description</h2>
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Job Summary *</label>
                            <textarea required rows="4" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Brief overview of the role and what the candidate will be doing..."></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Key Responsibilities *</label>
                            <textarea required rows="6" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="• List key responsibilities&#10;• One per line&#10;• Use bullet points"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Required Skills & Qualifications *</label>
                            <textarea required rows="6" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="• Required skills and qualifications&#10;• Education requirements&#10;• Technical skills&#10;• Soft skills"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Qualifications</label>
                            <textarea rows="4" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="• Nice-to-have skills&#10;• Additional certifications&#10;• Bonus qualifications"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Benefits & Perks</label>
                            <textarea rows="4" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="• Health insurance&#10;• 401k matching&#10;• Flexible work hours&#10;• Professional development"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Application Details -->
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Application Details</h2>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Application Deadline</label>
                            <input type="date" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email *</label>
                            <input type="email" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="<EMAIL>">
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4 pt-6 border-t">
                    <button type="button" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                        Save as Draft
                    </button>
                    <button type="submit" class="px-8 py-3 bg-primary text-white rounded-lg hover:bg-secondary transition font-medium">
                        Post Job
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Form submission
        document.getElementById('jobPostForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Show success message
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Posting Job...';
            submitBtn.disabled = true;

            // Collect form data
            const formData = collectJobFormData();

            // Save job to localStorage
            saveJobToStorage(formData);

            setTimeout(() => {
                alert('Job posted successfully! Your job is now live and visible to candidates.');
                // Redirect to jobs page to show the posted job
                window.location.href = 'jobs.html';
            }, 2000);
        });

        // Function to collect form data
        function collectJobFormData() {
            const form = document.getElementById('jobPostForm');

            // Get current timestamp for job ID and posting date
            const now = new Date();
            const jobId = now.getTime(); // Use timestamp as unique ID

            // Get form values by querying specific elements
            const companyName = form.querySelector('input[placeholder="Enter company name"]').value;
            const companyWebsite = form.querySelector('input[placeholder="https://www.company.com"]').value;
            const industry = form.querySelector('select').value; // First select is industry
            const jobTitle = form.querySelector('input[placeholder*="Senior Software Engineer"]').value;
            const jobCategory = form.querySelectorAll('select')[1].value; // Second select
            const employmentType = form.querySelectorAll('select')[2].value; // Third select
            const experienceLevel = form.querySelectorAll('select')[3].value; // Fourth select
            const workLocation = form.querySelectorAll('select')[4].value; // Fifth select
            const location = form.querySelector('input[placeholder*="New York, NY"]').value;
            const salaryMin = form.querySelector('input[placeholder="Min"]').value;
            const salaryMax = form.querySelector('input[placeholder="Max"]').value;
            const salaryPeriod = form.querySelectorAll('select')[5].value; // Sixth select
            const jobSummary = form.querySelectorAll('textarea')[0].value;
            const responsibilities = form.querySelectorAll('textarea')[1].value;
            const requirements = form.querySelectorAll('textarea')[2].value;
            const benefits = form.querySelectorAll('textarea')[4].value;
            const deadline = form.querySelector('input[type="date"]').value;
            const contactEmail = form.querySelector('input[type="email"]').value;

            // Calculate average salary for display
            const minSal = parseInt(salaryMin) || 0;
            const maxSal = parseInt(salaryMax) || 0;
            const avgSalary = maxSal > 0 ? Math.round((minSal + maxSal) / 2) : minSal;

            // Extract form data
            const jobData = {
                id: jobId,
                title: jobTitle,
                company: companyName,
                location: location,
                salary: avgSalary,
                salaryRange: getSalaryRange(avgSalary),
                posted: now.toISOString().split('T')[0], // Current date in YYYY-MM-DD format
                applicants: 0, // New job starts with 0 applicants
                description: jobSummary,
                skills: extractSkills(requirements), // Extract skills from requirements
                type: capitalizeFirst(employmentType), // Job Type (Full-time, Part-time, etc.)
                experience: capitalizeFirst(experienceLevel), // Experience Level
                website: companyWebsite,
                industry: capitalizeFirst(industry),
                category: jobCategory,
                workLocation: workLocation,
                salaryMin: minSal,
                salaryMax: maxSal,
                salaryPeriod: salaryPeriod,
                responsibilities: responsibilities,
                requirements: requirements,
                benefits: benefits,
                deadline: deadline,
                contactEmail: contactEmail,
                postedBy: 'employer', // Mark as employer-posted
                status: 'active' // Job status
            };

            return jobData;
        }

        // Helper function to capitalize first letter
        function capitalizeFirst(str) {
            if (!str) return '';
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // Function to determine salary range category
        function getSalaryRange(salary) {
            if (salary >= 100000) return "100k+";
            if (salary >= 75000) return "75k-100k";
            if (salary >= 50000) return "50k-75k";
            if (salary >= 25000) return "25k-50k";
            return "Under 25k";
        }

        // Function to extract skills from requirements text
        function extractSkills(requirementsText) {
            const commonSkills = [
                'JavaScript', 'Python', 'Java', 'React', 'Node.js', 'Angular', 'Vue.js',
                'HTML', 'CSS', 'PHP', 'C++', 'C#', 'Ruby', 'Go', 'Swift', 'Kotlin',
                'SQL', 'MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'AWS', 'Azure',
                'Docker', 'Kubernetes', 'Git', 'Linux', 'Windows', 'MacOS',
                'Machine Learning', 'AI', 'Data Science', 'Analytics', 'Statistics',
                'Project Management', 'Agile', 'Scrum', 'Leadership', 'Communication',
                'Marketing', 'Sales', 'Design', 'UI/UX', 'Photoshop', 'Figma'
            ];

            const foundSkills = [];
            const text = requirementsText.toLowerCase();

            commonSkills.forEach(skill => {
                if (text.includes(skill.toLowerCase())) {
                    foundSkills.push(skill);
                }
            });

            // Limit to 5 skills maximum
            return foundSkills.slice(0, 5);
        }

        // Function to save job to localStorage
        function saveJobToStorage(jobData) {
            try {
                // Get existing jobs from localStorage
                let existingJobs = JSON.parse(localStorage.getItem('postedJobs')) || [];

                // Add new job to the beginning of the array (most recent first)
                existingJobs.unshift(jobData);

                // Save back to localStorage
                localStorage.setItem('postedJobs', JSON.stringify(existingJobs));

                console.log('Job saved successfully:', jobData);
            } catch (error) {
                console.error('Error saving job to localStorage:', error);
            }
        }

        // Initialize auth (but don't require login for posting jobs)
        document.addEventListener('DOMContentLoaded', function() {
            if (window.DashboardAuth && window.DashboardAuth.isAuthenticated()) {
                window.DashboardAuth.init({
                    autoProtect: false,
                    showUserInfo: true,
                    userInfoId: 'userInfo'
                });
            }
        });
    </script>
</body>
</html>
