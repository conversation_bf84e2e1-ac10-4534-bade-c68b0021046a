# Email Setup Instructions for Job Applications

## Current Status
The job application form is now working and collects all applicant information. The email functionality is ready for integration. To enable real email sending, follow the instructions below.

## Option 1: EmailJS (Recommended - Free & Easy)

### Step 1: Create EmailJS Account
1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Sign up for a free account
3. Create a new email service (Gmail, Outlook, etc.)
4. Create an email template

### Step 2: Get Your EmailJS Credentials
- Service ID: `your_service_id`
- Template ID: `your_template_id`
- Public Key: `your_public_key`

### Step 3: Add EmailJS Script to jobs.html
Add this script tag in the `<head>` section of jobs.html:
```html
<script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
```

### Step 4: Initialize EmailJS
Add this code after the EmailJS script:
```javascript
<script>
    emailjs.init('YOUR_PUBLIC_KEY'); // Replace with your public key
</script>
```

### Step 5: Update the sendApplicationEmail Function
Replace the placeholder code in `sendApplicationEmail` function with:

```javascript
function sendApplicationEmail(applicationData) {
    return emailjs.send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', {
        to_email: '<EMAIL>', // Replace with your admin email
        subject: `New Job Application: ${applicationData.jobTitle} at ${applicationData.company}`,
        applicant_name: applicationData.fullName,
        applicant_email: applicationData.email,
        applicant_phone: applicationData.phone,
        job_title: applicationData.jobTitle,
        company: applicationData.company,
        experience: applicationData.experience,
        skills: applicationData.skills,
        interest: applicationData.interest,
        expected_salary: applicationData.expectedSalary,
        notice_period: applicationData.noticePeriod,
        resume: applicationData.resume,
        comments: applicationData.comments,
        submitted_at: applicationData.submittedAt
    });
}
```

### Step 6: Create Email Template in EmailJS
Create a template with these variables:
- `{{to_email}}`
- `{{subject}}`
- `{{applicant_name}}`
- `{{applicant_email}}`
- `{{applicant_phone}}`
- `{{job_title}}`
- `{{company}}`
- `{{experience}}`
- `{{skills}}`
- `{{interest}}`
- `{{expected_salary}}`
- `{{notice_period}}`
- `{{resume}}`
- `{{comments}}`
- `{{submitted_at}}`

## Option 2: Backend Integration

If you prefer a backend solution, you can:

1. Create a backend API endpoint (Node.js, PHP, Python, etc.)
2. Replace the `sendApplicationEmail` function to make an API call
3. Handle email sending on the server side

Example API call:
```javascript
function sendApplicationEmail(applicationData) {
    return fetch('/api/send-application', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(applicationData)
    });
}
```

## Configuration

### Admin Email Address
Update the admin email address in the `sendApplicationEmail` function:
```javascript
const ADMIN_EMAIL = '<EMAIL>'; // Replace with actual admin email
```

### Email Template
The system automatically creates a formatted email with all application details including:
- Job title and company
- Applicant personal information
- Experience and skills
- Interest in position
- Salary expectations
- Notice period
- Resume/CV information
- Additional comments
- Submission timestamp

## Testing

1. Open jobs.html in your browser
2. Click "Apply Now" on any job
3. Fill out the application form
4. Submit the form
5. Check the browser console for application data
6. Once EmailJS is configured, check your admin email

## Troubleshooting

- Check browser console for any JavaScript errors
- Verify EmailJS credentials are correct
- Ensure email template variables match the data being sent
- Check spam folder for emails
- Verify admin email address is correct

## Security Notes

- EmailJS has rate limits on free accounts
- Consider implementing CAPTCHA for production use
- Validate form data on both client and server side
- Store sensitive credentials securely (not in client-side code for production)
