<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USA EasyNaukri4U - Find Your Dream Job in America</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-primary font-medium">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-primary transition flex items-center">
                            For Employers <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="dashboard/employer.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">Employer Dashboard</a>
                            <a href="post-job.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">Post a Job</a>
                        </div>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-primary transition">Sign In</a>
                    <a href="signup.html" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition">Sign Up</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-primary font-medium">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
                <a href="dashboard/employer.html" class="block py-2 text-gray-700">For Employers</a>
                <div class="border-t pt-2 mt-2">
                    <a href="login.html" class="block py-2 text-gray-700">Sign In</a>
                    <a href="signup.html" class="block py-2 text-primary font-medium">Sign Up</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-primary to-secondary text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Find Your Dream Job in <span class="text-accent">America</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-blue-100">
                Connect with top employers across the United States. Your career journey starts here.
            </p>
            
            <!-- Search Bar -->
            <div class="max-w-4xl mx-auto bg-white rounded-lg p-4 shadow-xl">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="Job title, keywords, or company"
                                   class="search-input w-full pl-10 pr-4 py-3 text-gray-900 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-map-marker-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="City, state, or zip code"
                                   class="search-input w-full pl-10 pr-4 py-3 text-gray-900 rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                    </div>
                    <button class="search-btn bg-accent text-white px-8 py-3 rounded-lg hover:bg-yellow-500 transition font-medium">
                        <i class="fas fa-search mr-2"></i>Search Jobs
                    </button>
                </div>

                <!-- Quick Filters -->
                <div class="flex flex-wrap gap-2 mt-4 justify-center">
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Remote</span>
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Full-time</span>
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Part-time</span>
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Contract</span>
                    <span class="quick-filter bg-blue-50 text-primary px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-blue-100 transition">Internship</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-primary mb-2">50K+</div>
                    <div class="text-gray-600">Active Jobs</div>
                </div>
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-primary mb-2">25K+</div>
                    <div class="text-gray-600">Companies</div>
                </div>
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-primary mb-2">100K+</div>
                    <div class="text-gray-600">Job Seekers</div>
                </div>
                <div>
                    <div class="text-3xl md:text-4xl font-bold text-primary mb-2">95%</div>
                    <div class="text-gray-600">Success Rate</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Jobs Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Jobs</h2>
                <p class="text-xl text-gray-600">Discover opportunities from top companies</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Job Card 1 -->
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fab fa-google text-blue-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Senior Software Engineer</h3>
                                <p class="text-gray-600">Google Inc.</p>
                            </div>
                        </div>
                        <button class="text-gray-400 hover:text-danger transition">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Mountain View, CA</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-dollar-sign mr-2"></i>
                            <span>$150,000 - $200,000</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-clock mr-2"></i>
                            <span>Full-time</span>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">JavaScript</span>
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">React</span>
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">Node.js</span>
                    </div>
                    <button class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition">
                        Apply Now
                    </button>
                </div>

                <!-- Job Card 2 -->
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fab fa-microsoft text-red-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Product Manager</h3>
                                <p class="text-gray-600">Microsoft</p>
                            </div>
                        </div>
                        <button class="text-gray-400 hover:text-danger transition">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Seattle, WA</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-dollar-sign mr-2"></i>
                            <span>$120,000 - $160,000</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-clock mr-2"></i>
                            <span>Full-time</span>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Strategy</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Analytics</span>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Leadership</span>
                    </div>
                    <button class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition">
                        Apply Now
                    </button>
                </div>

                <!-- Job Card 3 -->
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fab fa-amazon text-purple-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">Data Scientist</h3>
                                <p class="text-gray-600">Amazon</p>
                            </div>
                        </div>
                        <button class="text-gray-400 hover:text-danger transition">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Austin, TX</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-dollar-sign mr-2"></i>
                            <span>$130,000 - $180,000</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-clock mr-2"></i>
                            <span>Full-time</span>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">Python</span>
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">ML</span>
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">SQL</span>
                    </div>
                    <button class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition">
                        Apply Now
                    </button>
                </div>
            </div>

            <div class="text-center">
                <a href="jobs.html" class="inline-flex items-center bg-white text-primary border-2 border-primary px-6 py-3 rounded-lg hover:bg-primary hover:text-white transition">
                    View All Jobs <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Browse by Category</h2>
                <p class="text-xl text-gray-600">Find jobs in your field of expertise</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                <div class="text-center group cursor-pointer">
                    <div class="bg-blue-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-code text-2xl text-blue-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Technology</h3>
                    <p class="text-sm text-gray-600">12,450 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-green-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-chart-line text-2xl text-green-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Finance</h3>
                    <p class="text-sm text-gray-600">8,230 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-red-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-heartbeat text-2xl text-red-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Healthcare</h3>
                    <p class="text-sm text-gray-600">6,890 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-purple-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-bullhorn text-2xl text-purple-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Marketing</h3>
                    <p class="text-sm text-gray-600">5,670 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-yellow-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-graduation-cap text-2xl text-yellow-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Education</h3>
                    <p class="text-sm text-gray-600">4,320 jobs</p>
                </div>

                <div class="text-center group cursor-pointer">
                    <div class="bg-indigo-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-primary group-hover:text-white transition">
                        <i class="fas fa-palette text-2xl text-indigo-600 group-hover:text-white"></i>
                    </div>
                    <h3 class="font-medium text-gray-900 mb-1">Design</h3>
                    <p class="text-sm text-gray-600">3,450 jobs</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Everything You Need to Land Your Dream Job</h2>
                <p class="text-xl text-gray-600">Comprehensive tools and resources to accelerate your career</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Resume Builder Feature -->
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-8 text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Professional Resume Builder</h3>
                    <p class="text-gray-600 mb-6">Create stunning, ATS-friendly resumes with our easy-to-use builder. Choose from professional templates and get hired faster.</p>
                    <ul class="text-left text-gray-600 mb-6 space-y-2">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>ATS-Optimized Templates</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Real-time Preview</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>PDF Export</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Industry-Specific Formats</li>
                    </ul>
                    <a href="resume-builder.html" class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition inline-block">
                        Build Resume <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Study Materials Feature -->
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-8 text-center">
                    <div class="bg-green-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-graduation-cap text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Study Materials & Prep</h3>
                    <p class="text-gray-600 mb-6">Master interview skills with our comprehensive study materials, practice tests, and mock interviews.</p>
                    <ul class="text-left text-gray-600 mb-6 space-y-2">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>500+ Practice Questions</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Mock Interviews</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Technical & Soft Skills</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Video Tutorials</li>
                    </ul>
                    <a href="study-material.html" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition inline-block">
                        Start Learning <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Dashboard Feature -->
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition p-8 text-center">
                    <div class="bg-purple-100 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-tachometer-alt text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Personal Dashboard</h3>
                    <p class="text-gray-600 mb-6">Track your job applications, manage your profile, and get personalized job recommendations all in one place.</p>
                    <ul class="text-left text-gray-600 mb-6 space-y-2">
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Application Tracking</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Job Recommendations</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Profile Management</li>
                        <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Interview Scheduling</li>
                    </ul>
                    <a href="login.html" class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition inline-block">
                        Access Dashboard <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
                <p class="text-xl text-gray-600">Get hired in 4 simple steps</p>
            </div>

            <div class="grid md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">1</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Create Your Profile</h3>
                    <p class="text-gray-600">Sign up and build your professional profile with our easy-to-use tools.</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">2</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Search & Apply</h3>
                    <p class="text-gray-600">Browse thousands of jobs and apply with one click using your profile.</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">3</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Prepare & Practice</h3>
                    <p class="text-gray-600">Use our study materials and mock interviews to ace your interviews.</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">4</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Get Hired</h3>
                    <p class="text-gray-600">Land your dream job and start your new career journey.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose USA EasyNaukri4U?</h2>
                <p class="text-xl text-gray-600">The most comprehensive job portal in America</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-shield-alt text-3xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Trusted Platform</h3>
                    <p class="text-gray-600">Verified companies and secure application process. Your data is safe with us.</p>
                </div>

                <div class="text-center">
                    <div class="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-rocket text-3xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Fast Results</h3>
                    <p class="text-gray-600">Get matched with relevant jobs instantly. Our AI-powered system finds the perfect fit.</p>
                </div>

                <div class="text-center">
                    <div class="bg-purple-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-users text-3xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Expert Support</h3>
                    <p class="text-gray-600">24/7 customer support and career guidance from industry experts.</p>
                </div>

                <div class="text-center">
                    <div class="bg-orange-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-chart-line text-3xl text-orange-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Career Growth</h3>
                    <p class="text-gray-600">Continuous learning resources and career development tools to advance your career.</p>
                </div>
            </div>

            <!-- Additional Features Grid -->
            <div class="mt-16 grid md:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-mobile-alt text-2xl text-primary mr-3"></i>
                        <h4 class="text-lg font-semibold text-gray-900">Mobile Optimized</h4>
                    </div>
                    <p class="text-gray-600">Search and apply for jobs on the go with our mobile-friendly platform.</p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-bell text-2xl text-primary mr-3"></i>
                        <h4 class="text-lg font-semibold text-gray-900">Smart Alerts</h4>
                    </div>
                    <p class="text-gray-600">Get notified instantly when new jobs matching your criteria are posted.</p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-analytics text-2xl text-primary mr-3"></i>
                        <h4 class="text-lg font-semibold text-gray-900">Application Insights</h4>
                    </div>
                    <p class="text-gray-600">Track your application status and get insights to improve your success rate.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- For Employers Section -->
    <section class="py-16 bg-gradient-to-r from-secondary to-primary text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl md:text-4xl font-bold mb-6">Hire Top Talent</h2>
                    <p class="text-xl text-blue-100 mb-8">Connect with qualified candidates and build your dream team. Our platform makes hiring simple and effective.</p>
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-accent mr-3 text-xl"></i>
                            <span class="text-lg">Access to 100K+ qualified candidates</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-accent mr-3 text-xl"></i>
                            <span class="text-lg">Advanced filtering and search tools</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-accent mr-3 text-xl"></i>
                            <span class="text-lg">Dedicated account management</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-accent mr-3 text-xl"></i>
                            <span class="text-lg">Competitive pricing plans</span>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="dashboard/employer.html" class="bg-accent text-gray-900 px-8 py-3 rounded-lg hover:bg-yellow-400 transition font-medium text-center">
                            Employer Dashboard
                        </a>
                        <a href="signup.html" class="bg-white/10 backdrop-blur-sm text-white px-8 py-3 rounded-lg hover:bg-white/20 transition font-medium text-center border border-white/20">
                            Post a Job
                        </a>
                    </div>
                </div>
                <div class="text-center">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-8">
                        <div class="grid grid-cols-2 gap-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent mb-2">25K+</div>
                                <div class="text-blue-100">Companies Trust Us</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent mb-2">48hrs</div>
                                <div class="text-blue-100">Average Time to Hire</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent mb-2">95%</div>
                                <div class="text-blue-100">Employer Satisfaction</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-accent mb-2">24/7</div>
                                <div class="text-blue-100">Support Available</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Success Stories Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Success Stories</h2>
                <p class="text-xl text-gray-600">Real people, real success stories</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-md p-8">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face" alt="Sarah Johnson" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">Sarah Johnson</h4>
                            <p class="text-gray-600 text-sm">Software Engineer at Google</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"EasyNaukri4U helped me land my dream job at Google. The resume builder and interview prep materials were incredibly helpful!"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-8">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="Michael Chen" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">Michael Chen</h4>
                            <p class="text-gray-600 text-sm">Data Scientist at Microsoft</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"The study materials and mock interviews gave me the confidence I needed. Got hired within 2 weeks of using the platform!"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md p-8">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face" alt="Emily Rodriguez" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-semibold text-gray-900">Emily Rodriguez</h4>
                            <p class="text-gray-600 text-sm">Marketing Manager at Amazon</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-4">"Amazing platform! The job recommendations were spot-on and the application process was seamless. Highly recommended!"</p>
                    <div class="flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="bg-gradient-to-r from-primary to-secondary rounded-2xl p-12 text-white">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Stay Updated</h2>
                <p class="text-xl text-blue-100 mb-8">Get the latest job opportunities and career tips delivered to your inbox</p>
                <div class="max-w-md mx-auto">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <input type="email" placeholder="Enter your email address"
                               class="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:ring-2 focus:ring-accent focus:outline-none">
                        <button class="bg-accent text-gray-900 px-8 py-3 rounded-lg hover:bg-yellow-400 transition font-medium">
                            Subscribe
                        </button>
                    </div>
                    <p class="text-sm text-blue-100 mt-4">No spam, unsubscribe at any time</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities across the United States.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <li><a href="dashboard/candidate.html" class="hover:text-white transition">My Dashboard</a></li>
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-material.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="dashboard/employer.html" class="hover:text-white transition">Employer Dashboard</a></li>
                        <li><a href="post-job.html" class="hover:text-white transition">Post a Job</a></li>
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help-center.html" class="hover:text-white transition">Help Center</a></li>
                        <li><a href="help-center.html" class="hover:text-white transition">Contact Us</a></li>
                        <li><a href="privacy-policy.html" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
</body>
</html>
