<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-primary transition flex items-center">
                            For Employers <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="login-dashboard.html?redirect=dashboard/employer.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-t-lg">Dashboard</a>
                            <a href="post-job.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-b-lg">Post a Job</a>
                        </div>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-primary transition">Sign In</a>
                    <a href="signup.html" class="text-primary font-medium">Sign Up</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-gray-700">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
                <a href="dashboard/employer.html" class="block py-2 text-gray-700">For Employers</a>
                <div class="border-t pt-2 mt-2">
                    <a href="login.html" class="block py-2 text-gray-700">Sign In</a>
                    <a href="signup.html" class="block py-2 text-primary font-medium">Sign Up</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Create Your Account</h2>
                <p class="text-gray-600">Join thousands of job seekers and employers</p>
            </div>

            <!-- Signup Form -->
            <div class="bg-white rounded-lg shadow-md p-8">
                <form id="signupForm" class="space-y-6">
                    <!-- User Type Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">I am a:</label>
                        <div class="flex space-x-4">
                            <label class="flex items-center">
                                <input type="radio" name="userType" value="candidate" checked class="text-primary focus:ring-primary">
                                <span class="ml-2 text-gray-700">Job Seeker</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="userType" value="employer" class="text-primary focus:ring-primary">
                                <span class="ml-2 text-gray-700">Employer</span>
                            </label>
                        </div>
                    </div>

                    <!-- Name Fields -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                            <input type="text" id="firstName" name="firstName" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="John">
                            <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                        </div>
                        <div>
                            <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                            <input type="text" id="lastName" name="lastName" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Doe">
                            <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                        </div>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <div class="relative">
                            <i class="fas fa-envelope absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="email" id="email" name="email" required
                                   class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <div class="relative">
                            <i class="fas fa-phone absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="tel" id="phone" name="phone" required
                                   class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="+****************">
                        </div>
                        <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <div class="relative">
                            <i class="fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="password" id="password" name="password" required
                                   class="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Create a strong password">
                            <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                    onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength mt-2">
                            <div class="flex space-x-1">
                                <div class="h-1 flex-1 bg-gray-200 rounded"></div>
                                <div class="h-1 flex-1 bg-gray-200 rounded"></div>
                                <div class="h-1 flex-1 bg-gray-200 rounded"></div>
                                <div class="h-1 flex-1 bg-gray-200 rounded"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Password strength: <span id="strength-text">Weak</span></p>
                        </div>
                        <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">Confirm Password</label>
                        <div class="relative">
                            <i class="fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" required
                                   class="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Confirm your password">
                            <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                    onclick="togglePassword('confirmPassword')">
                                <i class="fas fa-eye" id="confirmPassword-eye"></i>
                            </button>
                        </div>
                        <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div>
                        <label class="flex items-start">
                            <input type="checkbox" name="terms" required class="mt-1 rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-sm text-gray-700">
                                I agree to the <a href="#" class="text-primary hover:underline">Terms of Service</a> 
                                and <a href="#" class="text-primary hover:underline">Privacy Policy</a>
                            </span>
                        </label>
                        <div class="error-message hidden text-red-500 text-sm mt-1"></div>
                    </div>

                    <!-- Marketing Emails -->
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="marketing" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-sm text-gray-700">
                                Send me job alerts and marketing emails
                            </span>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="w-full bg-primary text-white py-3 rounded-lg hover:bg-secondary transition font-medium">
                        <i class="fas fa-user-plus mr-2"></i>Create Account
                    </button>

                    <!-- Divider -->
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Or sign up with</span>
                        </div>
                    </div>

                    <!-- Social Signup -->
                    <div class="grid grid-cols-2 gap-3">
                        <button type="button" class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition">
                            <i class="fab fa-google text-red-500 mr-2"></i>
                            <span class="text-gray-700">Google</span>
                        </button>
                        <button type="button" class="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition">
                            <i class="fab fa-linkedin text-blue-600 mr-2"></i>
                            <span class="text-gray-700">LinkedIn</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Sign In Link -->
            <div class="text-center">
                <p class="text-gray-600">
                    Already have an account? 
                    <a href="login.html" class="text-primary hover:underline font-medium">Sign in</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="flex items-center space-x-2 mb-4 md:mb-0">
                    <div class="bg-primary text-white p-2 rounded-lg">
                        <i class="fas fa-briefcase text-xl"></i>
                    </div>
                    <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                </div>
                <div class="flex space-x-6 text-gray-400">
                    <a href="#" class="hover:text-white transition">Privacy Policy</a>
                    <a href="#" class="hover:text-white transition">Terms of Service</a>
                    <a href="#" class="hover:text-white transition">Help Center</a>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-6 pt-6 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        // Toggle password visibility
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const eye = document.getElementById(fieldId + '-eye');
            
            if (field.type === 'password') {
                field.type = 'text';
                eye.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                eye.className = 'fas fa-eye';
            }
        }

        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;
            const checks = [
                password.length >= 8,
                /[a-z]/.test(password),
                /[A-Z]/.test(password),
                /[0-9]/.test(password),
                /[^A-Za-z0-9]/.test(password)
            ];
            
            strength = checks.filter(Boolean).length;
            
            const strengthBars = document.querySelectorAll('.password-strength .h-1');
            const strengthText = document.getElementById('strength-text');
            
            // Reset all bars
            strengthBars.forEach(bar => {
                bar.className = 'h-1 flex-1 bg-gray-200 rounded';
            });
            
            // Fill bars based on strength
            const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];
            const texts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
            
            for (let i = 0; i < Math.min(strength, 4); i++) {
                strengthBars[i].className = `h-1 flex-1 ${colors[Math.min(strength - 1, 3)]} rounded`;
            }
            
            strengthText.textContent = texts[Math.min(strength, 4)];
            strengthText.className = `text-xs ${strength < 2 ? 'text-red-500' : strength < 3 ? 'text-orange-500' : strength < 4 ? 'text-yellow-500' : 'text-green-500'}`;
        }

        // Password input event listener
        document.getElementById('password').addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });

        // Form validation
        function validateSignupForm(form) {
            const password = form.password.value;
            const confirmPassword = form.confirmPassword.value;
            
            if (password !== confirmPassword) {
                const confirmField = form.confirmPassword;
                const errorElement = confirmField.parentNode.querySelector('.error-message');
                confirmField.classList.add('border-red-500');
                errorElement.textContent = 'Passwords do not match';
                errorElement.classList.remove('hidden');
                return false;
            }
            
            if (password.length < 8) {
                const passwordField = form.password;
                const errorElement = passwordField.parentNode.querySelector('.error-message');
                passwordField.classList.add('border-red-500');
                errorElement.textContent = 'Password must be at least 8 characters long';
                errorElement.classList.remove('hidden');
                return false;
            }
            
            return EasyNaukri.validateForm(form);
        }

        // Form submission
        document.getElementById('signupForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateSignupForm(this)) {
                const formData = new FormData(this);
                const userType = formData.get('userType');
                
                // Simulate signup process
                EasyNaukri.showNotification('Creating your account...', 'info');
                
                setTimeout(() => {
                    EasyNaukri.showNotification('Account created successfully!', 'success');
                    
                    // Redirect based on user type
                    setTimeout(() => {
                        if (userType === 'employer') {
                            window.location.href = 'dashboard/employer.html';
                        } else {
                            window.location.href = 'dashboard/candidate.html';
                        }
                    }, 1000);
                }, 2000);
            }
        });

        // Social signup handlers
        document.querySelectorAll('button[type="button"]').forEach(button => {
            if (button.textContent.includes('Google') || button.textContent.includes('LinkedIn')) {
                button.addEventListener('click', function() {
                    const provider = this.textContent.includes('Google') ? 'Google' : 'LinkedIn';
                    EasyNaukri.showNotification(`Redirecting to ${provider}...`, 'info');
                    
                    // Redirect to OAuth provider
                    setTimeout(() => {
                        EasyNaukri.showNotification('Social signup feature coming soon!', 'info');
                    }, 1500);
                });
            }
        });
    </script>
</body>
</html>
