<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Select Dashboard - USA.EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="js/auth.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                            <i class="fas fa-briefcase text-white text-sm"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>
                
                <!-- User Info and Logout -->
                <div class="flex items-center space-x-4">
                    <div id="userInfo"></div>
                    <button id="logoutBtn" class="text-red-600 hover:text-red-700 transition">
                        <i class="fas fa-sign-out-alt mr-1"></i>Logout
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Dashboard Selection -->
    <div class="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl w-full">
            <div class="text-center mb-12">
                <div class="mx-auto h-20 w-20 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-tachometer-alt text-white text-2xl"></i>
                </div>
                <h2 class="text-3xl font-extrabold text-gray-900 mb-4">
                    Select Your Dashboard
                </h2>
                <p class="text-lg text-gray-600">
                    Choose the dashboard that matches your role
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Candidate Dashboard -->
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-8 text-center group cursor-pointer" onclick="selectDashboard('dashboard/candidate.html')">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <i class="fas fa-user text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Candidate Dashboard</h3>
                    <p class="text-gray-600 mb-6">
                        Manage your job applications, saved jobs, resume, and profile settings.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-2 mb-6">
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Job Applications</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Saved Jobs</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Resume Builder</li>
                        <li><i class="fas fa-check text-green-500 mr-2"></i>Interview Tracking</li>
                    </ul>
                    <button class="w-full bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition font-medium">
                        Access Candidate Dashboard
                    </button>
                </div>

                <!-- Employer Dashboard -->
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-8 text-center group cursor-pointer" onclick="selectDashboard('dashboard/employer.html')">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <i class="fas fa-building text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Employer Dashboard</h3>
                    <p class="text-gray-600 mb-6">
                        Post jobs, manage applications, view analytics, and handle company profile.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-2 mb-6">
                        <li><i class="fas fa-check text-blue-500 mr-2"></i>Job Posting</li>
                        <li><i class="fas fa-check text-blue-500 mr-2"></i>Application Management</li>
                        <li><i class="fas fa-check text-blue-500 mr-2"></i>Analytics & Reports</li>
                        <li><i class="fas fa-check text-blue-500 mr-2"></i>Company Profile</li>
                    </ul>
                    <button class="w-full bg-primary text-white py-3 rounded-lg hover:bg-secondary transition font-medium">
                        Access Employer Dashboard
                    </button>
                </div>

                <!-- Admin Dashboard -->
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-8 text-center group cursor-pointer" onclick="selectDashboard('dashboard/admin.html')">
                    <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <i class="fas fa-user-shield text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Admin Dashboard</h3>
                    <p class="text-gray-600 mb-6">
                        System administration, user management, content moderation, and settings.
                    </p>
                    <ul class="text-sm text-gray-500 space-y-2 mb-6">
                        <li><i class="fas fa-check text-red-500 mr-2"></i>User Management</li>
                        <li><i class="fas fa-check text-red-500 mr-2"></i>Content Moderation</li>
                        <li><i class="fas fa-check text-red-500 mr-2"></i>System Analytics</li>
                        <li><i class="fas fa-check text-red-500 mr-2"></i>Platform Settings</li>
                    </ul>
                    <button class="w-full bg-red-500 text-white py-3 rounded-lg hover:bg-red-600 transition font-medium">
                        Access Admin Dashboard
                    </button>
                </div>
            </div>

            <!-- Quick Access Links -->
            <div class="mt-12 text-center">
                <p class="text-gray-600 mb-4">Quick Access:</p>
                <div class="flex justify-center space-x-4">
                    <a href="index.html" class="text-primary hover:text-secondary transition">
                        <i class="fas fa-home mr-1"></i>Home
                    </a>
                    <a href="jobs.html" class="text-primary hover:text-secondary transition">
                        <i class="fas fa-search mr-1"></i>Find Jobs
                    </a>
                    <a href="resume-builder.html" class="text-primary hover:text-secondary transition">
                        <i class="fas fa-file-alt mr-1"></i>Resume Builder
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize authentication (but don't auto-protect this page)
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is authenticated
            if (!window.DashboardAuth.isAuthenticated()) {
                window.location.href = 'login-dashboard.html';
                return;
            }

            // Initialize with custom settings
            window.DashboardAuth.init({
                autoProtect: false, // Don't auto-protect this selection page
                showUserInfo: true,
                logoutButtonId: 'logoutBtn',
                userInfoId: 'userInfo'
            });
        });

        function selectDashboard(dashboardUrl) {
            // Add loading state
            const button = event.target.closest('.group').querySelector('button');
            const originalText = button.textContent;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
            button.disabled = true;

            // Simulate loading delay for better UX
            setTimeout(() => {
                window.location.href = dashboardUrl;
            }, 500);
        }

        // Add hover effects
        document.querySelectorAll('.group').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('transform', 'scale-105');
            });
            
            card.addEventListener('mouseleave', function() {
                this.classList.remove('transform', 'scale-105');
            });
        });
    </script>
</body>
</html>
