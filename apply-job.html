<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply for Job - USA.EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/auth.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="flex items-center space-x-6">
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">
                        <i class="fas fa-arrow-left mr-1"></i>Back to Jobs
                    </a>
                    <div id="userInfo"></div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Application Form -->
    <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <!-- Job Info Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex items-start justify-between">
                <div>
                    <h1 id="jobTitle" class="text-2xl font-bold text-gray-900 mb-2">Software Engineer</h1>
                    <p id="companyName" class="text-lg text-gray-600 mb-4">TechCorp Inc.</p>
                    <div class="flex flex-wrap gap-4 text-sm text-gray-500">
                        <span><i class="fas fa-map-marker-alt mr-1"></i>San Francisco, CA</span>
                        <span><i class="fas fa-briefcase mr-1"></i>Full-time</span>
                        <span><i class="fas fa-dollar-sign mr-1"></i>$80,000 - $120,000</span>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Application Status</div>
                    <div class="text-green-600 font-medium">Ready to Apply</div>
                </div>
            </div>
        </div>

        <!-- Application Form -->
        <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Job Application</h2>
            
            <form id="applicationForm" class="space-y-6">
                <!-- Personal Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                            <input type="text" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                            <input type="text" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                            <input type="tel" required class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                    </div>
                </div>

                <!-- Resume Upload -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Resume & Documents</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Resume/CV *</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600 mb-2">Drag and drop your resume here, or click to browse</p>
                                <input type="file" accept=".pdf,.doc,.docx" class="hidden" id="resumeUpload">
                                <button type="button" onclick="document.getElementById('resumeUpload').click()" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                                    Choose File
                                </button>
                                <p class="text-xs text-gray-500 mt-2">Supported formats: PDF, DOC, DOCX (Max 5MB)</p>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Cover Letter</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition">
                                <i class="fas fa-file-alt text-2xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600 mb-2">Optional: Upload your cover letter</p>
                                <input type="file" accept=".pdf,.doc,.docx" class="hidden" id="coverLetterUpload">
                                <button type="button" onclick="document.getElementById('coverLetterUpload').click()" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition">
                                    Choose File
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Why are you interested in this position?</label>
                            <textarea rows="4" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Tell us what interests you about this role and our company..."></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Salary Expectations (Optional)</label>
                            <input type="text" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="e.g., $80,000 - $100,000">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">When can you start?</label>
                            <select class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent">
                                <option value="">Select availability</option>
                                <option value="immediately">Immediately</option>
                                <option value="2weeks">2 weeks notice</option>
                                <option value="1month">1 month</option>
                                <option value="2months">2 months</option>
                                <option value="other">Other (please specify in comments)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Additional Comments</label>
                            <textarea rows="3" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Any additional information you'd like to share..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Terms and Submit -->
                <div class="border-t pt-6">
                    <div class="mb-6">
                        <label class="flex items-start space-x-3">
                            <input type="checkbox" required class="mt-1 rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="text-sm text-gray-600">
                                I agree to the <a href="#" class="text-primary hover:underline">Terms of Service</a> and 
                                <a href="#" class="text-primary hover:underline">Privacy Policy</a>. I consent to the processing 
                                of my personal data for recruitment purposes.
                            </span>
                        </label>
                    </div>
                    
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="window.history.back()" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">
                            Cancel
                        </button>
                        <button type="submit" class="px-8 py-3 bg-primary text-white rounded-lg hover:bg-secondary transition font-medium">
                            Submit Application
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Get job details from URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const jobTitle = urlParams.get('job') || 'Software Engineer';
            const company = urlParams.get('company') || 'TechCorp Inc.';
            
            document.getElementById('jobTitle').textContent = jobTitle;
            document.getElementById('companyName').textContent = company;
            document.title = `Apply for ${jobTitle} - USA.EasyNaukri4U`;

            // Initialize auth if available
            if (window.DashboardAuth && window.DashboardAuth.isAuthenticated()) {
                window.DashboardAuth.init({
                    autoProtect: false,
                    showUserInfo: true,
                    userInfoId: 'userInfo'
                });
            }
        });

        // Handle file uploads
        document.getElementById('resumeUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const parent = this.closest('.border-dashed');
                parent.innerHTML = `
                    <i class="fas fa-file-pdf text-3xl text-green-600 mb-2"></i>
                    <p class="text-green-600 font-medium">${file.name}</p>
                    <p class="text-sm text-gray-500">File uploaded successfully</p>
                    <button type="button" onclick="this.closest('.border-dashed').querySelector('input').click()" class="text-primary hover:underline text-sm mt-2">
                        Change file
                    </button>
                `;
            }
        });

        document.getElementById('coverLetterUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const parent = this.closest('.border-dashed');
                parent.innerHTML = `
                    <i class="fas fa-file-alt text-2xl text-green-600 mb-2"></i>
                    <p class="text-green-600 font-medium">${file.name}</p>
                    <p class="text-sm text-gray-500">Cover letter uploaded</p>
                    <button type="button" onclick="this.closest('.border-dashed').querySelector('input').click()" class="text-primary hover:underline text-sm mt-2">
                        Change file
                    </button>
                `;
            }
        });

        // Handle form submission
        document.getElementById('applicationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Submitting Application...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                alert('Application submitted successfully! You will receive a confirmation email shortly.');
                window.location.href = 'jobs.html';
            }, 2000);
        });
    </script>
</body>
</html>
