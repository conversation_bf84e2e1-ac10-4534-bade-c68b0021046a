<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employer Dashboard - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="../js/auth.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Dashboard Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                        <span class="text-sm text-gray-500 ml-2">| Employer Dashboard</span>
                    </div>
                </div>

                <!-- Dashboard Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#dashboard" class="text-primary font-medium hover:text-secondary transition">
                        <i class="fas fa-tachometer-alt mr-1"></i>Dashboard
                    </a>
                    <a href="#jobs" class="text-gray-700 hover:text-primary transition">
                        <i class="fas fa-briefcase mr-1"></i>My Jobs
                    </a>
                    <a href="#candidates" class="text-gray-700 hover:text-primary transition">
                        <i class="fas fa-users mr-1"></i>Candidates
                    </a>
                    <a href="#company" class="text-gray-700 hover:text-primary transition">
                        <i class="fas fa-building mr-1"></i>Company
                    </a>
                </div>

                <!-- User Profile -->
                <div class="hidden md:flex items-center space-x-4">
                    <!-- Notification Bell -->
                    <div class="relative">
                        <button class="text-gray-700 hover:text-primary transition relative notification-btn">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 bg-danger text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">4</span>
                        </button>
                        <!-- Notification Dropdown -->
                        <div class="notification-dropdown hidden absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
                            <div class="p-4 border-b">
                                <h3 class="font-semibold text-gray-900">Notifications</h3>
                            </div>
                            <div class="max-h-64 overflow-y-auto">
                                <div class="p-4 border-b hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-blue-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">New candidate applied: John Smith</p>
                                            <p class="text-xs text-gray-500">5 minutes ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-b hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-briefcase text-green-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Job posting approved and published</p>
                                            <p class="text-xs text-gray-500">2 hours ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border-b hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-star text-yellow-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Company profile viewed 25 times</p>
                                            <p class="text-xs text-gray-500">4 hours ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 hover:bg-gray-50">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-chart-line text-purple-600 text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-900">Weekly hiring report available</p>
                                            <p class="text-xs text-gray-500">1 day ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-4 border-t">
                                <a href="#" class="text-primary text-sm hover:underline">View all notifications</a>
                            </div>
                        </div>
                    </div>

                    <!-- User Info Display -->
                    <div id="userInfo" class="hidden md:block">
                        <!-- User info will be populated by auth.js -->
                    </div>

                    <!-- User Profile Dropdown -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-primary transition">
                            <div class="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="font-medium">Account</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">Company Profile</a>
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">Settings</a>
                            <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">Billing</a>
                            <div class="border-t"></div>
                            <a href="../index.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-home mr-2"></i>Back to Website
                            </a>
                            <button id="logoutBtn" class="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="#dashboard" class="block py-2 text-primary font-medium">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                <a href="#jobs" class="block py-2 text-gray-700">
                    <i class="fas fa-briefcase mr-2"></i>My Jobs
                </a>
                <a href="#candidates" class="block py-2 text-gray-700">
                    <i class="fas fa-users mr-2"></i>Candidates
                </a>
                <a href="#company" class="block py-2 text-gray-700">
                    <i class="fas fa-building mr-2"></i>Company
                </a>
                <div class="border-t pt-2 mt-2">
                    <a href="#" class="block py-2 text-gray-700">Company Profile</a>
                    <a href="../index.html" class="block py-2 text-gray-700">Back to Website</a>
                    <button onclick="window.DashboardAuth.logout()" class="block py-2 text-red-600 w-full text-left">Sign Out</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Dashboard Container -->
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-8">
                    <img src="https://images.unsplash.com/photo-1560179707-f14e90ef3623?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Company Logo" class="w-12 h-12 rounded-lg">
                    <div>
                        <h3 class="font-semibold text-gray-900">TechCorp Inc.</h3>
                        <p class="text-sm text-gray-600">Technology</p>
                    </div>
                </div>

                <nav class="space-y-2">
                    <a href="#overview" class="dashboard-nav-link active flex items-center space-x-3 px-4 py-3 text-primary bg-blue-50 rounded-lg">
                        <i class="fas fa-chart-pie"></i>
                        <span>Overview</span>
                    </a>
                    <a href="#jobs" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-briefcase"></i>
                        <span>Job Postings</span>
                    </a>
                    <a href="#applications" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-file-alt"></i>
                        <span>Applications</span>
                    </a>
                    <a href="#candidates" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-users"></i>
                        <span>Candidates</span>
                    </a>
                    <a href="#analytics" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                    <a href="#company" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-building"></i>
                        <span>Company Profile</span>
                    </a>
                    <a href="#settings" class="dashboard-nav-link flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Overview Section -->
            <div id="overview" class="dashboard-section">
                <!-- Header -->
                <div class="flex items-center justify-between mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Dashboard Overview</h1>
                        <p class="text-gray-600 mt-1">Welcome back! Here's what's happening with your job postings.</p>
                    </div>
                    <button class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-plus mr-2"></i>Post New Job
                    </button>
                </div>

                <!-- Stats Cards -->
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Active Jobs</p>
                                <p class="text-3xl font-bold text-gray-900">12</p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+2 this month
                                </p>
                            </div>
                            <div class="bg-blue-100 text-blue-600 p-3 rounded-lg">
                                <i class="fas fa-briefcase text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Total Applications</p>
                                <p class="text-3xl font-bold text-gray-900">248</p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+15% this week
                                </p>
                            </div>
                            <div class="bg-green-100 text-green-600 p-3 rounded-lg">
                                <i class="fas fa-file-alt text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Interviews Scheduled</p>
                                <p class="text-3xl font-bold text-gray-900">18</p>
                                <p class="text-blue-600 text-sm mt-1">
                                    <i class="fas fa-calendar mr-1"></i>5 this week
                                </p>
                            </div>
                            <div class="bg-purple-100 text-purple-600 p-3 rounded-lg">
                                <i class="fas fa-calendar-check text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-600 text-sm">Hired Candidates</p>
                                <p class="text-3xl font-bold text-gray-900">7</p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+3 this month
                                </p>
                            </div>
                            <div class="bg-yellow-100 text-yellow-600 p-3 rounded-lg">
                                <i class="fas fa-user-check text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity & Quick Actions -->
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Recent Applications -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Recent Applications</h3>
                                <a href="#applications" class="text-primary hover:underline text-sm">View All</a>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center space-x-4">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Candidate" class="w-10 h-10 rounded-full">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">John Smith</h4>
                                        <p class="text-gray-600 text-sm">Applied for Senior Developer</p>
                                    </div>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">New</span>
                                </div>

                                <div class="flex items-center space-x-4">
                                    <img src="https://images.unsplash.com/photo-*************-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Candidate" class="w-10 h-10 rounded-full">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Sarah Johnson</h4>
                                        <p class="text-gray-600 text-sm">Applied for UX Designer</p>
                                    </div>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Reviewed</span>
                                </div>

                                <div class="flex items-center space-x-4">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Candidate" class="w-10 h-10 rounded-full">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Mike Davis</h4>
                                        <p class="text-gray-600 text-sm">Applied for Product Manager</p>
                                    </div>
                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">Interview</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-md">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-4">
                                <button class="bg-primary text-white p-4 rounded-lg hover:bg-secondary transition text-center">
                                    <i class="fas fa-plus text-2xl mb-2"></i>
                                    <p class="font-medium">Post Job</p>
                                </button>
                                <button class="bg-green-500 text-white p-4 rounded-lg hover:bg-green-600 transition text-center">
                                    <i class="fas fa-search text-2xl mb-2"></i>
                                    <p class="font-medium">Search Candidates</p>
                                </button>
                                <button class="bg-purple-500 text-white p-4 rounded-lg hover:bg-purple-600 transition text-center">
                                    <i class="fas fa-calendar text-2xl mb-2"></i>
                                    <p class="font-medium">Schedule Interview</p>
                                </button>
                                <button class="bg-orange-500 text-white p-4 rounded-lg hover:bg-orange-600 transition text-center">
                                    <i class="fas fa-chart-bar text-2xl mb-2"></i>
                                    <p class="font-medium">View Reports</p>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Postings Section -->
            <div id="jobs" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Job Postings</h2>
                    <button class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-plus mr-2"></i>Post New Job
                    </button>
                </div>

                <!-- Job Filters -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex flex-wrap items-center gap-4">
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option>All Status</option>
                            <option>Active</option>
                            <option>Paused</option>
                            <option>Expired</option>
                        </select>
                        <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option>All Departments</option>
                            <option>Engineering</option>
                            <option>Design</option>
                            <option>Marketing</option>
                        </select>
                        <input type="text" placeholder="Search jobs..." class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>

                <!-- Jobs List -->
                <div class="space-y-4">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Senior Software Engineer</h3>
                                <p class="text-gray-600">Engineering • Full-time • San Francisco, CA</p>
                            </div>
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Active</span>
                        </div>
                        <div class="grid md:grid-cols-4 gap-4 mb-4">
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900">45</p>
                                <p class="text-gray-600 text-sm">Applications</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900">12</p>
                                <p class="text-gray-600 text-sm">Shortlisted</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900">5</p>
                                <p class="text-gray-600 text-sm">Interviews</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900">2</p>
                                <p class="text-gray-600 text-sm">Hired</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <p class="text-gray-600 text-sm">Posted 5 days ago • Expires in 25 days</p>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Applications</button>
                                <button class="text-gray-600 hover:text-gray-800 text-sm">Edit</button>
                                <button class="text-red-600 hover:text-red-800 text-sm">Pause</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">UX/UI Designer</h3>
                                <p class="text-gray-600">Design • Full-time • Remote</p>
                            </div>
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Active</span>
                        </div>
                        <div class="grid md:grid-cols-4 gap-4 mb-4">
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900">32</p>
                                <p class="text-gray-600 text-sm">Applications</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900">8</p>
                                <p class="text-gray-600 text-sm">Shortlisted</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900">3</p>
                                <p class="text-gray-600 text-sm">Interviews</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900">1</p>
                                <p class="text-gray-600 text-sm">Hired</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <p class="text-gray-600 text-sm">Posted 3 days ago • Expires in 27 days</p>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Applications</button>
                                <button class="text-gray-600 hover:text-gray-800 text-sm">Edit</button>
                                <button class="text-red-600 hover:text-red-800 text-sm">Pause</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other sections would be added here -->
            <!-- Applications Section -->
            <div id="applications" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Applications</h2>
                    <div class="flex space-x-2">
                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                            <option>All Jobs</option>
                            <option>Senior Software Engineer</option>
                            <option>Product Manager</option>
                            <option>UX Designer</option>
                        </select>
                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                            <option>All Status</option>
                            <option>New</option>
                            <option>Reviewing</option>
                            <option>Shortlisted</option>
                            <option>Rejected</option>
                        </select>
                    </div>
                </div>

                <!-- Applications List -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Candidate</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-10 h-10 rounded-full">
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">John Smith</div>
                                                <div class="text-sm text-gray-500"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">Senior Software Engineer</div>
                                        <div class="text-sm text-gray-500">Engineering</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        2 days ago
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">New</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="text-sm font-medium text-gray-900">85%</div>
                                            <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                                <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-primary hover:underline">View</button>
                                        <button class="text-green-600 hover:underline">Shortlist</button>
                                        <button class="text-red-600 hover:underline">Reject</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="https://images.unsplash.com/photo-*************-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-10 h-10 rounded-full">
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">Sarah Johnson</div>
                                                <div class="text-sm text-gray-500"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">Product Manager</div>
                                        <div class="text-sm text-gray-500">Product</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        1 week ago
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Shortlisted</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="text-sm font-medium text-gray-900">92%</div>
                                            <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                                <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-primary hover:underline">View</button>
                                        <button class="text-green-600 hover:underline">Interview</button>
                                        <button class="text-red-600 hover:underline">Reject</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-10 h-10 rounded-full">
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">Mike Chen</div>
                                                <div class="text-sm text-gray-500"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">UX Designer</div>
                                        <div class="text-sm text-gray-500">Design</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        3 days ago
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Reviewing</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="text-sm font-medium text-gray-900">78%</div>
                                            <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 78%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-primary hover:underline">View</button>
                                        <button class="text-green-600 hover:underline">Shortlist</button>
                                        <button class="text-red-600 hover:underline">Reject</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="flex justify-center mt-6">
                    <nav class="flex space-x-2">
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">Previous</button>
                        <button class="px-3 py-2 bg-primary text-white rounded">1</button>
                        <button class="px-3 py-2 text-gray-700 hover:text-gray-900">2</button>
                        <button class="px-3 py-2 text-gray-700 hover:text-gray-900">3</button>
                        <button class="px-3 py-2 text-gray-500 hover:text-gray-700">Next</button>
                    </nav>
                </div>
            </div>

            <!-- Candidates Section -->
            <div id="candidates" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Candidate Database</h2>
                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-search mr-2"></i>Advanced Search
                    </button>
                </div>

                <!-- Search and Filters -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="grid md:grid-cols-4 gap-4">
                        <div>
                            <input type="text" placeholder="Search candidates..." class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        </div>
                        <div>
                            <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                <option>All Skills</option>
                                <option>JavaScript</option>
                                <option>Python</option>
                                <option>React</option>
                                <option>Node.js</option>
                            </select>
                        </div>
                        <div>
                            <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                <option>All Locations</option>
                                <option>San Francisco, CA</option>
                                <option>New York, NY</option>
                                <option>Seattle, WA</option>
                            </select>
                        </div>
                        <div>
                            <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                <option>All Experience</option>
                                <option>Entry Level</option>
                                <option>Mid Level</option>
                                <option>Senior Level</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Candidates Grid -->
                <div class="grid lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-16 h-16 rounded-full">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Alex Rodriguez</h3>
                                <p class="text-gray-600">Senior Full Stack Developer</p>
                                <p class="text-sm text-gray-500">San Francisco, CA</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">JavaScript</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">React</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Node.js</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">AWS</span>
                            </div>
                            <p class="text-gray-600 text-sm">5+ years experience building scalable web applications. Previously at Google and Meta.</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">$150k</span> expected salary
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Profile</button>
                                <button class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-secondary transition">Contact</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <img src="https://images.unsplash.com/photo-*************-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-16 h-16 rounded-full">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Emily Chen</h3>
                                <p class="text-gray-600">Product Designer</p>
                                <p class="text-sm text-gray-500">New York, NY</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">Figma</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">Sketch</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">Prototyping</span>
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">User Research</span>
                            </div>
                            <p class="text-gray-600 text-sm">Creative designer with 4 years experience in UX/UI design. Specialized in mobile app design.</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">$120k</span> expected salary
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Profile</button>
                                <button class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-secondary transition">Contact</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-16 h-16 rounded-full">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">David Kim</h3>
                                <p class="text-gray-600">DevOps Engineer</p>
                                <p class="text-sm text-gray-500">Seattle, WA</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">AWS</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Docker</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Kubernetes</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Terraform</span>
                            </div>
                            <p class="text-gray-600 text-sm">Infrastructure specialist with 6 years experience. Expert in cloud architecture and automation.</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">$140k</span> expected salary
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Profile</button>
                                <button class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-secondary transition">Contact</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-16 h-16 rounded-full">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Lisa Wang</h3>
                                <p class="text-gray-600">Data Scientist</p>
                                <p class="text-sm text-gray-500">Boston, MA</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">Python</span>
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">Machine Learning</span>
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">TensorFlow</span>
                                <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">SQL</span>
                            </div>
                            <p class="text-gray-600 text-sm">PhD in Computer Science with 3 years industry experience in ML and data analytics.</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">$135k</span> expected salary
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Profile</button>
                                <button class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-secondary transition">Contact</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-16 h-16 rounded-full">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">James Wilson</h3>
                                <p class="text-gray-600">Mobile Developer</p>
                                <p class="text-sm text-gray-500">Austin, TX</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs">React Native</span>
                                <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs">Swift</span>
                                <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs">Kotlin</span>
                                <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs">Flutter</span>
                            </div>
                            <p class="text-gray-600 text-sm">Mobile app developer with 4 years experience. Built apps with 1M+ downloads.</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">$125k</span> expected salary
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Profile</button>
                                <button class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-secondary transition">Contact</button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-16 h-16 rounded-full">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Maria Garcia</h3>
                                <p class="text-gray-600">Backend Engineer</p>
                                <p class="text-sm text-gray-500">Los Angeles, CA</p>
                            </div>
                        </div>
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">Java</span>
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">Spring Boot</span>
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">PostgreSQL</span>
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">Microservices</span>
                            </div>
                            <p class="text-gray-600 text-sm">Backend specialist with 5 years experience in enterprise applications and distributed systems.</p>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <span class="font-medium">$145k</span> expected salary
                            </div>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:underline text-sm">View Profile</button>
                                <button class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-secondary transition">Contact</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Load More -->
                <div class="text-center mt-8">
                    <button class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition">
                        Load More Candidates
                    </button>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics" class="dashboard-section hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Analytics & Reports</h2>

                <!-- Key Metrics -->
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Total Applications</p>
                                <p class="text-2xl font-bold text-gray-900">1,247</p>
                                <p class="text-sm text-green-600">+12% from last month</p>
                            </div>
                            <div class="bg-blue-100 text-blue-600 p-3 rounded-lg">
                                <i class="fas fa-file-alt text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Active Jobs</p>
                                <p class="text-2xl font-bold text-gray-900">23</p>
                                <p class="text-sm text-blue-600">5 new this week</p>
                            </div>
                            <div class="bg-green-100 text-green-600 p-3 rounded-lg">
                                <i class="fas fa-briefcase text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Interviews Scheduled</p>
                                <p class="text-2xl font-bold text-gray-900">89</p>
                                <p class="text-sm text-yellow-600">15 this week</p>
                            </div>
                            <div class="bg-yellow-100 text-yellow-600 p-3 rounded-lg">
                                <i class="fas fa-calendar text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Hires Made</p>
                                <p class="text-2xl font-bold text-gray-900">34</p>
                                <p class="text-sm text-purple-600">3 this month</p>
                            </div>
                            <div class="bg-purple-100 text-purple-600 p-3 rounded-lg">
                                <i class="fas fa-user-check text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="grid lg:grid-cols-2 gap-8 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Application Trends</h3>
                        <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-chart-line text-4xl mb-2"></i>
                                <p>Application trends chart would be displayed here</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Performing Jobs</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">Senior Software Engineer</p>
                                    <p class="text-sm text-gray-600">156 applications</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-green-600">+23%</p>
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">Product Manager</p>
                                    <p class="text-sm text-gray-600">134 applications</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-blue-600">+18%</p>
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 72%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">UX Designer</p>
                                    <p class="text-sm text-gray-600">98 applications</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-purple-600">+15%</p>
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div class="bg-purple-500 h-2 rounded-full" style="width: 58%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Reports -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Detailed Reports</h3>
                        <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                            <i class="fas fa-download mr-2"></i>Export Report
                        </button>
                    </div>
                    <div class="grid md:grid-cols-3 gap-4">
                        <button class="p-4 border border-gray-300 rounded-lg hover:border-primary hover:bg-blue-50 transition text-left">
                            <div class="flex items-center space-x-3">
                                <div class="bg-blue-100 text-blue-600 p-2 rounded">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Hiring Funnel</p>
                                    <p class="text-sm text-gray-600">Application to hire conversion</p>
                                </div>
                            </div>
                        </button>
                        <button class="p-4 border border-gray-300 rounded-lg hover:border-primary hover:bg-blue-50 transition text-left">
                            <div class="flex items-center space-x-3">
                                <div class="bg-green-100 text-green-600 p-2 rounded">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Time to Hire</p>
                                    <p class="text-sm text-gray-600">Average hiring timeline</p>
                                </div>
                            </div>
                        </button>
                        <button class="p-4 border border-gray-300 rounded-lg hover:border-primary hover:bg-blue-50 transition text-left">
                            <div class="flex items-center space-x-3">
                                <div class="bg-purple-100 text-purple-600 p-2 rounded">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Source Analysis</p>
                                    <p class="text-sm text-gray-600">Where candidates come from</p>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Company Profile Section -->
            <div id="company" class="dashboard-section hidden">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Company Profile</h2>
                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-edit mr-2"></i>Edit Profile
                    </button>
                </div>

                <div class="grid lg:grid-cols-3 gap-8">
                    <!-- Company Info -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Basic Information -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h3>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                                    <input type="text" value="TechCorp Inc." class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                                    <input type="text" value="Technology" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Company Size</label>
                                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2" disabled>
                                        <option>1000-5000 employees</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Founded</label>
                                    <input type="text" value="2010" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                                    <input type="url" value="https://techcorp.com" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                                    <input type="text" value="San Francisco, CA" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Company Description -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">About Company</h3>
                            <textarea rows="6" class="w-full border border-gray-300 rounded-lg px-3 py-2" readonly>TechCorp Inc. is a leading technology company specializing in innovative software solutions for enterprise clients. Founded in 2010, we've grown to over 2,000 employees worldwide and serve Fortune 500 companies across various industries. Our mission is to transform businesses through cutting-edge technology and exceptional service.</textarea>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Company Logo -->
                        <div class="bg-white rounded-lg shadow-md p-6 text-center">
                            <div class="w-24 h-24 bg-primary rounded-lg mx-auto mb-4 flex items-center justify-center">
                                <span class="text-white text-2xl font-bold">TC</span>
                            </div>
                            <h3 class="font-semibold text-gray-900">TechCorp Inc.</h3>
                            <p class="text-gray-600 text-sm">Technology Company</p>
                            <button class="mt-3 text-primary hover:underline text-sm">Change Logo</button>
                        </div>

                        <!-- Company Stats -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Stats</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Employees</span>
                                    <span class="font-semibold text-gray-900">2,150</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Open Positions</span>
                                    <span class="font-semibold text-gray-900">23</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Offices</span>
                                    <span class="font-semibold text-gray-900">8</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Founded</span>
                                    <span class="font-semibold text-gray-900">2010</span>
                                </div>
                            </div>
                        </div>

                        <!-- Social Media -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Social Media</h3>
                            <div class="space-y-3">
                                <div class="flex items-center space-x-3">
                                    <div class="bg-blue-100 text-blue-600 p-2 rounded">
                                        <i class="fab fa-linkedin"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 text-sm">LinkedIn</p>
                                        <p class="text-gray-600 text-xs">50K followers</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="bg-blue-100 text-blue-600 p-2 rounded">
                                        <i class="fab fa-twitter"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 text-sm">Twitter</p>
                                        <p class="text-gray-600 text-xs">25K followers</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="bg-gray-100 text-gray-600 p-2 rounded">
                                        <i class="fab fa-github"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 text-sm">GitHub</p>
                                        <p class="text-gray-600 text-xs">Open source projects</p>
                                    </div>
                                </div>
                            </div>
                            <button class="mt-3 text-primary hover:underline text-sm">+ Add Social Media</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings" class="dashboard-section hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Settings</h2>

                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Account Settings -->
                    <div class="space-y-6">
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Settings</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Company Email</label>
                                    <input type="email" value="<EMAIL>" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                                    <input type="tel" value="+****************" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                                    <button class="text-primary hover:underline text-sm">Change Password</button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Billing & Subscription</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900">Professional Plan</p>
                                        <p class="text-sm text-gray-600">Up to 50 job postings per month</p>
                                    </div>
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Active</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Next billing date</span>
                                    <span class="font-semibold text-gray-900">Jan 15, 2025</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600">Monthly cost</span>
                                    <span class="font-semibold text-gray-900">$299/month</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                                        Upgrade Plan
                                    </button>
                                    <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition">
                                        View Invoices
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Team Management</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Team Members</p>
                                        <p class="text-sm text-gray-600">Manage who can access your account</p>
                                    </div>
                                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">
                                        Manage Team
                                    </button>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-*************-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-8 h-8 rounded-full">
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">Sarah Johnson</p>
                                                <p class="text-xs text-gray-600">HR Manager</p>
                                            </div>
                                        </div>
                                        <span class="text-xs text-gray-500">Admin</span>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="Profile" class="w-8 h-8 rounded-full">
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">Mike Chen</p>
                                                <p class="text-xs text-gray-600">Recruiter</p>
                                            </div>
                                        </div>
                                        <span class="text-xs text-gray-500">Member</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification & Preferences -->
                    <div class="space-y-6">
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">New Applications</p>
                                        <p class="text-sm text-gray-600">Get notified when candidates apply</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Interview Reminders</p>
                                        <p class="text-sm text-gray-600">Reminders for scheduled interviews</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Weekly Reports</p>
                                        <p class="text-sm text-gray-600">Weekly hiring analytics summary</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Marketing Updates</p>
                                        <p class="text-sm text-gray-600">Product updates and tips</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Job Posting Preferences</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Default Job Duration</label>
                                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                        <option>30 days</option>
                                        <option>60 days</option>
                                        <option>90 days</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Auto-renewal</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="radio" name="auto-renewal" class="text-primary focus:ring-primary" checked>
                                            <span class="ml-2 text-gray-700">Auto-renew expiring jobs</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="auto-renewal" class="text-primary focus:ring-primary">
                                            <span class="ml-2 text-gray-700">Manual renewal only</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Featured Job Listings</p>
                                        <p class="text-sm text-gray-600">Highlight your jobs for better visibility</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Privacy & Security</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Two-Factor Authentication</p>
                                        <p class="text-sm text-gray-600">Add extra security to your account</p>
                                    </div>
                                    <button class="text-primary hover:underline text-sm">Enable 2FA</button>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Login Activity</p>
                                        <p class="text-sm text-gray-600">View recent login attempts</p>
                                    </div>
                                    <button class="text-primary hover:underline text-sm">View Activity</button>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">Data Export</p>
                                        <p class="text-sm text-gray-600">Download your company data</p>
                                    </div>
                                    <button class="text-primary hover:underline text-sm">Export Data</button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Actions</h3>
                            <div class="space-y-3">
                                <button class="w-full bg-yellow-50 text-yellow-700 p-3 rounded-lg hover:bg-yellow-100 transition text-left">
                                    <i class="fas fa-pause mr-2"></i>Pause Account
                                </button>
                                <button class="w-full bg-red-50 text-red-700 p-3 rounded-lg hover:bg-red-100 transition text-left">
                                    <i class="fas fa-trash mr-2"></i>Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="mt-8 flex justify-end">
                    <button class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            EasyNaukri.initializeDashboardNavigation();
        });
    </script>
</body>
</html>
