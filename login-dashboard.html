<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Login - USA.EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-gray-700 hover:text-primary transition">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-primary transition flex items-center">
                            For Employers <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="login-dashboard.html?redirect=dashboard/employer.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-t-lg">Dashboard</a>
                            <a href="post-job.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-b-lg">Post a Job</a>
                        </div>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-primary transition">Sign In</a>
                    <a href="signup.html" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition">Sign Up</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-gray-700">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-gray-700">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
                <a href="dashboard/employer.html" class="block py-2 text-gray-700">For Employers</a>
                <div class="border-t pt-2 mt-2">
                    <a href="login.html" class="block py-2 text-gray-700">Sign In</a>
                    <a href="signup.html" class="block py-2 text-primary font-medium">Sign Up</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Login Form -->
    <div class="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="mx-auto h-20 w-20 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                    <i class="fas fa-user-shield text-white text-2xl"></i>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Dashboard Access
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Please sign in to access the dashboard
                </p>
            </div>
            
            <form class="mt-8 space-y-6" id="loginForm">
                <div class="rounded-md shadow-sm space-y-4">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                        <input id="username" name="username" type="text" required 
                               class="appearance-none rounded-lg relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" 
                               placeholder="Enter your username">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <div class="relative">
                            <input id="password" name="password" type="password" required 
                                   class="appearance-none rounded-lg relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm pr-10" 
                                   placeholder="Enter your password">
                            <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="eyeIcon"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Error Message -->
                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span id="errorText">Invalid username or password</span>
                    </div>
                </div>

                <!-- Success Message -->
                <div id="successMessage" class="hidden bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span>Login successful! Redirecting...</span>
                    </div>
                </div>

                <div>
                    <button type="submit" id="loginBtn" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition duration-200">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt text-primary-light group-hover:text-primary-light"></i>
                        </span>
                        <span id="loginBtnText">Sign In</span>
                        <i id="loginSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                    </button>
                </div>

                <!-- Login Credentials -->
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">Login Credentials:</h4>
                    <div class="text-sm text-blue-700 space-y-1">
                        <div><strong>Username:</strong> admin</div>
                        <div><strong>Password:</strong> 123456</div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Authentication system
        const AUTH_CONFIG = {
            credentials: {
                'admin': '123456'
            },
            sessionKey: 'dashboardAuth',
            redirectUrl: 'dashboard/employer.html'
        };

        // Check if already logged in
        document.addEventListener('DOMContentLoaded', function() {
            if (isAuthenticated()) {
                const redirectUrl = getRedirectUrl();
                window.location.href = redirectUrl;
            }
        });

        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.className = 'fas fa-eye-slash text-gray-400 hover:text-gray-600';
            } else {
                passwordInput.type = 'password';
                eyeIcon.className = 'fas fa-eye text-gray-400 hover:text-gray-600';
            }
        });

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginSpinner = document.getElementById('loginSpinner');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            // Hide previous messages
            errorMessage.classList.add('hidden');
            successMessage.classList.add('hidden');
            
            // Show loading state
            loginBtn.disabled = true;
            loginBtnText.textContent = 'Signing In...';
            loginSpinner.classList.remove('hidden');
            
            // Simulate authentication delay
            setTimeout(() => {
                if (authenticateUser(username, password)) {
                    // Success
                    successMessage.classList.remove('hidden');
                    loginBtnText.textContent = 'Success!';
                    loginSpinner.classList.add('hidden');
                    
                    // Store authentication
                    storeAuthentication(username);
                    
                    // Redirect after delay
                    setTimeout(() => {
                        const redirectUrl = getRedirectUrl();
                        window.location.href = redirectUrl;
                    }, 1500);
                } else {
                    // Error
                    errorMessage.classList.remove('hidden');
                    loginBtn.disabled = false;
                    loginBtnText.textContent = 'Sign In';
                    loginSpinner.classList.add('hidden');
                    
                    // Clear password field
                    document.getElementById('password').value = '';
                }
            }, 1000);
        });

        // Authentication functions
        function authenticateUser(username, password) {
            return AUTH_CONFIG.credentials[username] === password;
        }

        function storeAuthentication(username) {
            const authData = {
                username: username,
                loginTime: new Date().getTime(),
                expiresIn: 24 * 60 * 60 * 1000 // 24 hours
            };
            localStorage.setItem(AUTH_CONFIG.sessionKey, JSON.stringify(authData));
        }

        function isAuthenticated() {
            const authData = getAuthData();
            if (!authData) return false;
            
            const now = new Date().getTime();
            const isExpired = now > (authData.loginTime + authData.expiresIn);
            
            if (isExpired) {
                clearAuthentication();
                return false;
            }
            
            return true;
        }

        function getAuthData() {
            try {
                const data = localStorage.getItem(AUTH_CONFIG.sessionKey);
                return data ? JSON.parse(data) : null;
            } catch (e) {
                return null;
            }
        }

        function clearAuthentication() {
            localStorage.removeItem(AUTH_CONFIG.sessionKey);
        }

        function getRedirectUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const redirect = urlParams.get('redirect');

            // If no redirect specified, show dashboard selection
            if (!redirect) {
                return 'dashboard-selection.html';
            }

            return redirect;
        }
    </script>
</body>
</html>
