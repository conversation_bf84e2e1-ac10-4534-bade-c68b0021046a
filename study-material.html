<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Study Materials - USA EasyNaukri4U</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#1e40af',
                        'accent': '#f59e0b',
                        'success': '#10b981',
                        'danger': '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">USA.EasyNaukri4U</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition">Home</a>
                    <a href="jobs.html" class="text-gray-700 hover:text-primary transition">Find Jobs</a>
                    <a href="study-material.html" class="text-primary font-medium">Study Materials</a>
                    <a href="resume-builder.html" class="text-gray-700 hover:text-primary transition">Resume Builder</a>
                    <div class="relative group">
                        <button class="text-gray-700 hover:text-primary transition flex items-center">
                            For Employers <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="login-dashboard.html?redirect=dashboard/employer.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-t-lg">Dashboard</a>
                            <a href="post-job.html" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-b-lg">Post a Job</a>
                        </div>
                    </div>
                </div>

                <!-- User Actions -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="login.html" class="text-gray-700 hover:text-primary transition">Sign In</a>
                    <a href="signup.html" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition">Sign Up</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-4 py-2 space-y-2">
                <a href="index.html" class="block py-2 text-gray-700">Home</a>
                <a href="jobs.html" class="block py-2 text-gray-700">Find Jobs</a>
                <a href="study-material.html" class="block py-2 text-primary font-medium">Study Materials</a>
                <a href="resume-builder.html" class="block py-2 text-gray-700">Resume Builder</a>
                <a href="dashboard/employer.html" class="block py-2 text-gray-700">For Employers</a>
                <div class="border-t pt-2 mt-2">
                    <a href="login.html" class="block py-2 text-gray-700">Sign In</a>
                    <a href="signup.html" class="block py-2 text-primary font-medium">Sign Up</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <section class="bg-gradient-to-r from-primary to-secondary text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Study Materials</h1>
            <p class="text-xl text-blue-100 mb-8">Comprehensive resources to ace your interviews and advance your career</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold">500+</div>
                    <div class="text-sm text-blue-100">Practice Questions</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold">50+</div>
                    <div class="text-sm text-blue-100">Study Topics</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold">100+</div>
                    <div class="text-sm text-blue-100">Video Tutorials</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Study Materials Content -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Tab Navigation -->
            <div class="flex flex-wrap justify-center mb-8 border-b">
                <button class="tab-btn active px-6 py-3 font-medium text-primary border-b-2 border-primary" data-tab="aptitude">
                    <i class="fas fa-calculator mr-2"></i>Aptitude & Reasoning
                </button>
                <button class="tab-btn px-6 py-3 font-medium text-gray-600 hover:text-primary border-b-2 border-transparent" data-tab="technical">
                    <i class="fas fa-code mr-2"></i>Technical Skills
                </button>
                <button class="tab-btn px-6 py-3 font-medium text-gray-600 hover:text-primary border-b-2 border-transparent" data-tab="interview">
                    <i class="fas fa-comments mr-2"></i>Interview Preparation
                </button>
                <button class="tab-btn px-6 py-3 font-medium text-gray-600 hover:text-primary border-b-2 border-transparent" data-tab="soft-skills">
                    <i class="fas fa-users mr-2"></i>Soft Skills
                </button>
            </div>

            <!-- Aptitude & Reasoning Tab -->
            <div id="aptitude" class="tab-content">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Quantitative Aptitude -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6">
                            <i class="fas fa-chart-line text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">Quantitative Aptitude</h3>
                            <p class="text-blue-100">Master mathematical concepts and problem-solving</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Number Systems & Algebra</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Geometry & Mensuration</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Time, Speed & Distance</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Profit & Loss</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-sm text-gray-600">Progress</span>
                                <span class="text-sm font-medium text-primary">65%</span>
                            </div>
                            <div class="bg-gray-200 rounded-full h-2 mb-4">
                                <div class="bg-primary h-2 rounded-full" style="width: 65%"></div>
                            </div>
                            <button class="w-full bg-primary text-white py-3 rounded-lg hover:bg-secondary transition">
                                Start Practice
                            </button>
                        </div>
                    </div>

                    <!-- Logical Reasoning -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6">
                            <i class="fas fa-brain text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">Logical Reasoning</h3>
                            <p class="text-purple-100">Enhance your logical thinking abilities</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Puzzles & Seating Arrangement</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Blood Relations</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Direction & Distance</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Coding-Decoding</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-sm text-gray-600">Progress</span>
                                <span class="text-sm font-medium text-purple-600">45%</span>
                            </div>
                            <div class="bg-gray-200 rounded-full h-2 mb-4">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 45%"></div>
                            </div>
                            <button class="w-full bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition">
                                Start Practice
                            </button>
                        </div>
                    </div>

                    <!-- Verbal Ability -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-6">
                            <i class="fas fa-book-open text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">Verbal Ability</h3>
                            <p class="text-green-100">Improve your English language skills</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Reading Comprehension</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Grammar & Vocabulary</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Sentence Correction</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Para Jumbles</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-sm text-gray-600">Progress</span>
                                <span class="text-sm font-medium text-green-600">80%</span>
                            </div>
                            <div class="bg-gray-200 rounded-full h-2 mb-4">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                            <button class="w-full bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition">
                                Start Practice
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Practice Sets -->
                <div class="mt-12">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Practice Test Series</h3>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-semibold text-gray-900">Mock Test 1</h4>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">Beginner</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Complete aptitude test covering all topics</p>
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span><i class="fas fa-clock mr-1"></i>60 minutes</span>
                                <span><i class="fas fa-question-circle mr-1"></i>50 questions</span>
                            </div>
                            <button class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition">
                                Take Test
                            </button>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-semibold text-gray-900">Mock Test 2</h4>
                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">Intermediate</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Advanced level questions for better preparation</p>
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span><i class="fas fa-clock mr-1"></i>90 minutes</span>
                                <span><i class="fas fa-question-circle mr-1"></i>75 questions</span>
                            </div>
                            <button class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition">
                                Take Test
                            </button>
                        </div>

                        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-semibold text-gray-900">Mock Test 3</h4>
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">Advanced</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4">Expert level test for final preparation</p>
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span><i class="fas fa-clock mr-1"></i>120 minutes</span>
                                <span><i class="fas fa-question-circle mr-1"></i>100 questions</span>
                            </div>
                            <button class="w-full bg-primary text-white py-2 rounded-lg hover:bg-secondary transition">
                                Take Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Skills Tab -->
            <div id="technical" class="tab-content hidden">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Programming Languages -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white p-6">
                            <i class="fas fa-code text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">Programming Languages</h3>
                            <p class="text-indigo-100">Master popular programming languages</p>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-3">
                                <div class="bg-gray-50 rounded-lg p-3 text-center hover:bg-primary hover:text-white transition cursor-pointer">
                                    <i class="fab fa-js-square text-2xl mb-2"></i>
                                    <div class="text-sm font-medium">JavaScript</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3 text-center hover:bg-primary hover:text-white transition cursor-pointer">
                                    <i class="fab fa-python text-2xl mb-2"></i>
                                    <div class="text-sm font-medium">Python</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3 text-center hover:bg-primary hover:text-white transition cursor-pointer">
                                    <i class="fab fa-java text-2xl mb-2"></i>
                                    <div class="text-sm font-medium">Java</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3 text-center hover:bg-primary hover:text-white transition cursor-pointer">
                                    <i class="fas fa-hashtag text-2xl mb-2"></i>
                                    <div class="text-sm font-medium">C++</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Structures & Algorithms -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white p-6">
                            <i class="fas fa-project-diagram text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">Data Structures & Algorithms</h3>
                            <p class="text-red-100">Core computer science concepts</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Arrays & Strings</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Linked Lists & Trees</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Sorting & Searching</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Dynamic Programming</span>
                                </div>
                            </div>
                            <button class="w-full bg-red-500 text-white py-3 rounded-lg hover:bg-red-600 transition">
                                Start Learning
                            </button>
                        </div>
                    </div>

                    <!-- System Design -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-teal-500 to-teal-600 text-white p-6">
                            <i class="fas fa-sitemap text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">System Design</h3>
                            <p class="text-teal-100">Learn to design scalable systems</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Scalability Principles</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Database Design</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Microservices</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Load Balancing</span>
                                </div>
                            </div>
                            <button class="w-full bg-teal-500 text-white py-3 rounded-lg hover:bg-teal-600 transition">
                                Start Learning
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Coding Challenges -->
                <div class="mt-12">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Coding Challenges</h3>
                    <div class="bg-white rounded-lg shadow-md p-8">
                        <div class="grid md:grid-cols-3 gap-8">
                            <div class="text-center">
                                <div class="bg-green-100 text-green-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-trophy text-2xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Easy Challenges</h4>
                                <p class="text-gray-600 text-sm mb-4">Perfect for beginners to start coding</p>
                                <button class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition">
                                    Start Easy
                                </button>
                            </div>
                            <div class="text-center">
                                <div class="bg-yellow-100 text-yellow-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-medal text-2xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Medium Challenges</h4>
                                <p class="text-gray-600 text-sm mb-4">Intermediate level problem solving</p>
                                <button class="bg-yellow-500 text-white px-6 py-2 rounded-lg hover:bg-yellow-600 transition">
                                    Try Medium
                                </button>
                            </div>
                            <div class="text-center">
                                <div class="bg-red-100 text-red-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-crown text-2xl"></i>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Hard Challenges</h4>
                                <p class="text-gray-600 text-sm mb-4">Expert level algorithmic problems</p>
                                <button class="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600 transition">
                                    Challenge Hard
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interview Preparation Tab -->
            <div id="interview" class="tab-content hidden">
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Common Interview Questions -->
                    <div class="bg-white rounded-lg shadow-md p-8">
                        <div class="flex items-center mb-6">
                            <div class="bg-blue-100 text-blue-600 p-3 rounded-lg mr-4">
                                <i class="fas fa-question-circle text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Common Interview Questions</h3>
                                <p class="text-gray-600">Practice the most frequently asked questions</p>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="border-l-4 border-primary pl-4">
                                <h4 class="font-medium text-gray-900">Tell me about yourself</h4>
                                <p class="text-gray-600 text-sm">Learn how to craft a compelling personal introduction</p>
                            </div>
                            <div class="border-l-4 border-primary pl-4">
                                <h4 class="font-medium text-gray-900">Why do you want this job?</h4>
                                <p class="text-gray-600 text-sm">Show your motivation and research about the company</p>
                            </div>
                            <div class="border-l-4 border-primary pl-4">
                                <h4 class="font-medium text-gray-900">What are your strengths?</h4>
                                <p class="text-gray-600 text-sm">Highlight your key skills with examples</p>
                            </div>
                        </div>
                        <button class="w-full mt-6 bg-primary text-white py-3 rounded-lg hover:bg-secondary transition">
                            View All Questions
                        </button>
                    </div>

                    <!-- Mock Interviews -->
                    <div class="bg-white rounded-lg shadow-md p-8">
                        <div class="flex items-center mb-6">
                            <div class="bg-green-100 text-green-600 p-3 rounded-lg mr-4">
                                <i class="fas fa-video text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Mock Interviews</h3>
                                <p class="text-gray-600">Practice with AI-powered interview simulation</p>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900">Technical Interview</h4>
                                    <span class="text-sm text-gray-500">45 min</span>
                                </div>
                                <p class="text-gray-600 text-sm">Coding questions and technical discussions</p>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900">Behavioral Interview</h4>
                                    <span class="text-sm text-gray-500">30 min</span>
                                </div>
                                <p class="text-gray-600 text-sm">Situational and behavioral questions</p>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900">HR Interview</h4>
                                    <span class="text-sm text-gray-500">20 min</span>
                                </div>
                                <p class="text-gray-600 text-sm">General questions about background and goals</p>
                            </div>
                        </div>
                        <button class="w-full mt-6 bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition">
                            Start Mock Interview
                        </button>
                    </div>
                </div>

                <!-- Interview Tips -->
                <div class="mt-12">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Interview Success Tips</h3>
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-md p-6 text-center">
                            <div class="bg-blue-100 text-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-search text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Research the Company</h4>
                            <p class="text-gray-600 text-sm">Learn about company culture, values, and recent news</p>
                        </div>
                        <div class="bg-white rounded-lg shadow-md p-6 text-center">
                            <div class="bg-green-100 text-green-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user-tie text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Dress Professionally</h4>
                            <p class="text-gray-600 text-sm">Make a great first impression with appropriate attire</p>
                        </div>
                        <div class="bg-white rounded-lg shadow-md p-6 text-center">
                            <div class="bg-purple-100 text-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-comments text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Practice Communication</h4>
                            <p class="text-gray-600 text-sm">Work on clear, confident, and concise responses</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Soft Skills Tab -->
            <div id="soft-skills" class="tab-content hidden">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Communication Skills -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-pink-500 to-pink-600 text-white p-6">
                            <i class="fas fa-comments text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">Communication Skills</h3>
                            <p class="text-pink-100">Master effective communication techniques</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Public Speaking</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Active Listening</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Written Communication</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Non-verbal Communication</span>
                                </div>
                            </div>
                            <button class="w-full bg-pink-500 text-white py-3 rounded-lg hover:bg-pink-600 transition">
                                Start Learning
                            </button>
                        </div>
                    </div>

                    <!-- Leadership Skills -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-6">
                            <i class="fas fa-crown text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">Leadership Skills</h3>
                            <p class="text-orange-100">Develop your leadership potential</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Team Management</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Decision Making</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Conflict Resolution</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Motivation Techniques</span>
                                </div>
                            </div>
                            <button class="w-full bg-orange-500 text-white py-3 rounded-lg hover:bg-orange-600 transition">
                                Start Learning
                            </button>
                        </div>
                    </div>

                    <!-- Time Management -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="bg-gradient-to-r from-cyan-500 to-cyan-600 text-white p-6">
                            <i class="fas fa-clock text-3xl mb-4"></i>
                            <h3 class="text-xl font-bold">Time Management</h3>
                            <p class="text-cyan-100">Optimize your productivity and efficiency</p>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Priority Setting</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Goal Planning</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Productivity Tools</span>
                                </div>
                                <div class="flex items-center text-gray-700">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span>Work-Life Balance</span>
                                </div>
                            </div>
                            <button class="w-full bg-cyan-500 text-white py-3 rounded-lg hover:bg-cyan-600 transition">
                                Start Learning
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Soft Skills Assessment -->
                <div class="mt-12">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Soft Skills Assessment</h3>
                    <div class="bg-white rounded-lg shadow-md p-8">
                        <div class="text-center mb-8">
                            <div class="bg-gradient-to-r from-primary to-secondary text-white w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-chart-pie text-3xl"></i>
                            </div>
                            <h4 class="text-xl font-bold text-gray-900 mb-2">Evaluate Your Soft Skills</h4>
                            <p class="text-gray-600">Take our comprehensive assessment to identify your strengths and areas for improvement</p>
                        </div>
                        <div class="grid md:grid-cols-4 gap-6 mb-8">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">25</div>
                                <div class="text-sm text-gray-600">Questions</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">15</div>
                                <div class="text-sm text-gray-600">Minutes</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">8</div>
                                <div class="text-sm text-gray-600">Skill Areas</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">100%</div>
                                <div class="text-sm text-gray-600">Free</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button class="bg-gradient-to-r from-primary to-secondary text-white px-8 py-3 rounded-lg hover:from-secondary hover:to-primary transition">
                                Start Assessment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="bg-primary text-white p-2 rounded-lg">
                            <i class="fas fa-briefcase text-xl"></i>
                        </div>
                        <span class="text-xl font-bold">USA.EasyNaukri4U</span>
                    </div>
                    <p class="text-gray-400 mb-4">Your trusted partner in finding the perfect job opportunities in the USA.</p>
                    <div class="flex space-x-4">
                        <a href="https://www.facebook.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Facebook">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="https://www.twitter.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Twitter">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="https://www.linkedin.com/company/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Connect with us on LinkedIn">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="https://www.instagram.com/easynaukri4u" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition" title="Follow us on Instagram">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Job Seekers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="jobs.html" class="hover:text-white transition">Browse Jobs</a></li>
                        <li><a href="dashboard/candidate.html" class="hover:text-white transition">My Dashboard</a></li>
                        <li><a href="resume-builder.html" class="hover:text-white transition">Resume Builder</a></li>
                        <li><a href="study-material.html" class="hover:text-white transition">Study Materials</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="dashboard/employer.html" class="hover:text-white transition">Employer Dashboard</a></li>
                        <li><a href="#" class="hover:text-white transition">Post a Job</a></li>
                        <li><a href="#" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition">Resources</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help-center.html" class="hover:text-white transition">Help Center</a></li>
                        <li><a href="help-center.html" class="hover:text-white transition">Contact Us</a></li>
                        <li><a href="privacy-policy.html" class="hover:text-white transition">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 USA.EasyNaukri4U. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            EasyNaukri.initializeTabs();
        });
    </script>
</body>
</html>
